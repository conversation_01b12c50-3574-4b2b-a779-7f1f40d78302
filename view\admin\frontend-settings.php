<?php
$page_title = '前端设置';
$current_page = 'frontend-settings';

// 设置默认值
$frontendSettings = $frontendSettings ?? [
    'site_title' => '',
    'site_description' => '',
    'promotion_enabled' => 0,
    'promotion_title' => '',
    'promotion_btn1_text' => '',
    'promotion_btn1_url' => '',
    'promotion_btn2_text' => '',
    'promotion_btn2_url' => '',
    'promotion_btn3_text' => '',
    'promotion_btn3_url' => '',
    'promotion_contact_text' => '',
    'promotion_contact_value' => '',
    'exchange_success_message' => '',
    'exchange_error_message' => '',
    'page_footer_notice' => '',
    // 兑换确认对话框
    'exchange_confirm_title' => '兑换确认',
    'exchange_confirm_content' => '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！',
    // 验证提示
    'empty_card_message' => '请输入卡密',
    // 兑换错误提示
    'card_not_exist_message' => '卡密不存在或已失效',
    'card_already_used_message' => '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！',
    'card_disabled_message' => '卡密已被禁用，请联系微信hzoedu888',
    'card_expired_message' => '卡密已过期',
    // 查询错误提示
    'query_disabled_message' => '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888',
    'card_not_exchanged_message' => '该卡密尚未兑换，请先兑换后再查看兑换记录',
    'no_exchange_record_message' => '未找到兑换记录',
    // 成功提示
    'exchange_success_message' => '兑换成功！',
    'query_success_message' => '查询成功',
    // 复制功能提示
    'copy_success_message' => '✅ 链接复制成功！',
    'copy_error_message' => '❌ 复制失败，请手动复制',
    // 网络错误提示
    'network_error_message' => '网络错误，请稍后重试',
];

$content = '
<!-- 页面头部 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h2 class="mb-1">前端设置</h2>
        <p class="text-muted mb-0">配置前端页面的显示内容和样式</p>
    </div>
    <div>
        <button class="btn btn-primary" onclick="saveFrontendSettings()">
            <i class="fas fa-save me-2"></i>保存全部设置
        </button>
    </div>
</div>

<!-- 前端设置标签页 -->
<div class="chart-container">
    <ul class="nav nav-tabs" id="frontendSettingsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                <i class="fas fa-bell me-2"></i>通知弹窗
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="promotion-tab" data-bs-toggle="tab" data-bs-target="#promotion" type="button" role="tab">
                <i class="fas fa-bullhorn me-2"></i>推广模块
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="popup-tab" data-bs-toggle="tab" data-bs-target="#popup" type="button" role="tab">
                <i class="fas fa-comment-dots me-2"></i>弹窗提示
            </button>
        </li>
    </ul>

    <div class="tab-content" id="frontendSettingsTabContent">
        <!-- 通知弹窗 -->
        <div class="tab-pane fade show active" id="basic" role="tabpanel">
            <div class="p-4">
                <form id="frontendForm">
                    <div class="row g-3">
                        <!-- 开关控制 -->
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">启用通知弹窗</label>
                                </div>
                                <div class="col-5">
                                    <div class="form-check form-switch d-flex align-items-center" style="min-height: 38px;">
                                        <input class="form-check-input" type="checkbox" name="notification_enabled" id="notificationEnabled" value="1" ' . (($frontendSettings['notification_enabled'] ?? 0) ? 'checked' : '') . '>
                                        <label class="form-check-label ms-2" for="notificationEnabled">启用通知弹窗</label>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">控制是否在前端页面显示通知弹窗</small>
                                </div>
                            </div>
                        </div>

                        <!-- 弹窗标题 -->
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">弹窗标题</label>
                                </div>
                                <div class="col-5">
                                    <input type="text" class="form-control" name="notification_title" value="' . htmlspecialchars($frontendSettings['notification_title'] ?? '系统通知') . '" placeholder="系统通知">
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">弹窗顶部显示的标题文字</small>
                                </div>
                            </div>
                        </div>

                        <!-- 自动关闭时间 -->
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-center h-100">自动关闭</label>
                                </div>
                                <div class="col-5">
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="notification_auto_close" value="' . (int)($frontendSettings['notification_auto_close'] ?? 5) . '" min="0" max="60" placeholder="5">
                                        <span class="input-group-text">秒</span>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-center h-100">弹窗自动关闭时间，设置为0表示不自动关闭</small>
                                </div>
                            </div>
                        </div>



                        <!-- 弹窗内容 -->
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">弹窗内容</label>
                                </div>
                                <div class="col-9">
                                    <div class="row g-2">
                                        <div class="col-7">
                                            <div class="position-relative">
                                                <textarea class="form-control" name="notification_content" id="notificationContent" rows="8" placeholder="请输入通知内容，支持HTML格式">' . htmlspecialchars($frontendSettings['notification_content'] ?? '') . '</textarea>
                                                <div class="mt-2">
                                                    <!-- 格式化按钮 -->
                                                    <div class="btn-group btn-group-sm mb-2" role="group">
                                                        <button type="button" class="btn btn-outline-secondary" onclick="insertHtmlTag(\'<strong>\', \'</strong>\')">
                                                            <i class="fas fa-bold"></i> 粗体
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="insertHtmlTag(\'<em>\', \'</em>\')">
                                                            <i class="fas fa-italic"></i> 斜体
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="insertHtmlTag(\'<br>\', \'\')">
                                                            <i class="fas fa-level-down-alt"></i> 换行
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary" onclick="insertHtmlTag(\'<p>\', \'</p>\')">
                                                            <i class="fas fa-paragraph"></i> 段落
                                                        </button>
                                                    </div>
                                                    <!-- 对齐和颜色按钮 -->
                                                    <div class="btn-group btn-group-sm mb-2" role="group">
                                                        <button type="button" class="btn btn-outline-secondary" onclick="insertHtmlTag(\'<div style=&quot;text-align: center;&quot;>\', \'</div>\')">
                                                            <i class="fas fa-align-center"></i> 居中
                                                        </button>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                                <i class="fas fa-palette"></i> 颜色
                                                            </button>
                                                            <ul class="dropdown-menu">
                                                                <li><a class="dropdown-item" href="#" onclick="insertColorTag(\'red\')"><span class="text-danger">●</span> 红色</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="insertColorTag(\'blue\')"><span class="text-primary">●</span> 蓝色</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="insertColorTag(\'green\')"><span class="text-success">●</span> 绿色</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="insertColorTag(\'orange\')"><span style="color: orange;">●</span> 橙色</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="insertColorTag(\'purple\')"><span style="color: purple;">●</span> 紫色</a></li>
                                                                <li><a class="dropdown-item" href="#" onclick="insertColorTag(\'gray\')"><span class="text-muted">●</span> 灰色</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-5">
                                            <div class="bg-light border rounded p-3 h-100">
                                                <small class="text-muted">
                                                    <span class="text-primary fw-bold">常用HTML标签：</span><br><br>
                                                    <code>&lt;p&gt;段落&lt;/p&gt;</code><br>
                                                    <code>&lt;strong&gt;粗体&lt;/strong&gt;</code><br>
                                                    <code>&lt;em&gt;斜体&lt;/em&gt;</code><br>
                                                    <code>&lt;br&gt;</code> 换行<br>
                                                    <code>&lt;div style="text-align: center;"&gt;居中&lt;/div&gt;</code><br>
                                                    <code>&lt;span style="color: red;"&gt;彩色文字&lt;/span&gt;</code><br>
                                                    <code>&lt;a href="链接"&gt;链接文字&lt;/a&gt;</code>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 弹窗模板选择 -->
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">弹窗模板</label>
                                </div>
                                <div class="col-9">
                                    <div class="row g-2">
                                        <!-- 喜讯通知模板 -->
                                        <div class="col-4">
                                            <div class="card notification-template" data-template="celebration" style="cursor: pointer;">
                                                <div class="card-body p-3 text-center">
                                                    <div class="mb-2">
                                                        <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                            <i class="fas fa-trophy text-white"></i>
                                                        </div>
                                                    </div>
                                                    <h6 class="card-title mb-2 text-success" style="font-size: 14px;">🎉 喜讯通知</h6>
                                                    <small class="text-muted d-block mb-2" style="font-size: 12px; line-height: 1.4;">
                                                        绿色主题，带庆祝元素<br>
                                                        适用于：活动成功、获奖通知、好消息发布
                                                    </small>
                                                    <div class="form-check d-flex justify-content-center">
                                                        <input class="form-check-input" type="radio" name="notification_style" value="celebration" ' . (($frontendSettings['notification_style'] ?? 'daily') == 'celebration' ? 'checked' : '') . '>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 紧急通知模板 -->
                                        <div class="col-4">
                                            <div class="card notification-template" data-template="urgent" style="cursor: pointer;">
                                                <div class="card-body p-3 text-center">
                                                    <div class="mb-2">
                                                        <div class="bg-danger bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                            <i class="fas fa-exclamation-triangle text-white"></i>
                                                        </div>
                                                    </div>
                                                    <h6 class="card-title mb-2 text-danger" style="font-size: 14px;">⚠️ 紧急通知</h6>
                                                    <small class="text-muted d-block mb-2" style="font-size: 12px; line-height: 1.4;">
                                                        红色主题，醒目警示<br>
                                                        适用于：系统维护、紧急公告、重要提醒
                                                    </small>
                                                    <div class="form-check d-flex justify-content-center">
                                                        <input class="form-check-input" type="radio" name="notification_style" value="urgent" ' . (($frontendSettings['notification_style'] ?? 'daily') == 'urgent' ? 'checked' : '') . '>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 日常通知模板 -->
                                        <div class="col-4">
                                            <div class="card notification-template" data-template="daily" style="cursor: pointer;">
                                                <div class="card-body p-3 text-center">
                                                    <div class="mb-2">
                                                        <div class="bg-primary bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                            <i class="fas fa-info-circle text-white"></i>
                                                        </div>
                                                    </div>
                                                    <h6 class="card-title mb-2 text-primary" style="font-size: 14px;">📢 日常通知</h6>
                                                    <small class="text-muted d-block mb-2" style="font-size: 12px; line-height: 1.4;">
                                                        蓝色主题，简洁清晰<br>
                                                        适用于：常规公告、使用提醒、一般信息
                                                    </small>
                                                    <div class="form-check d-flex justify-content-center">
                                                        <input class="form-check-input" type="radio" name="notification_style" value="daily" ' . (($frontendSettings['notification_style'] ?? 'daily') == 'daily' ? 'checked' : '') . '>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 预览区域 -->
                        <div class="col-12">
                            <div class="row g-1">
                                <div class="col-auto pe-3" style="min-width: 120px;">
                                    <label class="form-label mb-0 d-flex align-items-start pt-2">弹窗预览</label>
                                </div>
                                <div class="col-5">
                                    <div class="border rounded p-3 bg-light">
                                        <button type="button" class="btn btn-primary btn-sm" onclick="previewNotification()">
                                            <i class="fas fa-eye me-1"></i>预览弹窗效果
                                        </button>
                                        <div id="previewArea" class="mt-3" style="display: none;">
                                            <!-- 预览模态框将在这里动态生成 -->
                                        </div>
                                    </div>
                                </div>
                                <div class="col">
                                    <small class="text-muted d-flex align-items-start pt-2">点击预览按钮查看弹窗的实际显示效果，不同模板有不同的视觉风格</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- 保存按钮 -->
                <div class="p-4 pt-0 d-flex justify-content-end">
                    <button type="button" class="btn btn-primary" onclick="saveFrontendSettings()">
                        <i class="fas fa-save"></i> 保存设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 推广模块 -->
        <div class="tab-pane fade" id="promotion" role="tabpanel">
            <div class="p-4">
                <div class="row g-3">
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">推广模块</label>
                            </div>
                            <div class="col-5">
                                <div class="form-check form-switch d-flex align-items-center" style="min-height: 38px;">
                                    <input class="form-check-input" type="checkbox" name="promotion_enabled" id="promotionEnabled" value="1" ' . ($frontendSettings['promotion_enabled'] ? 'checked' : '') . '>
                                    <label class="form-check-label ms-2" for="promotionEnabled">启用推广模块</label>
                                </div>
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">兑换成功后显示推广内容</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">推广标题</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_title" value="' . htmlspecialchars($frontendSettings['promotion_title']) . '" placeholder="您还可以点击以下按钮获取更多免费资源">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">推广模块的标题文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮1文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_btn1_text" value="' . htmlspecialchars($frontendSettings['promotion_btn1_text']) . '" placeholder="电子资料包">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第一个推广按钮的显示文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮1链接</label>
                            </div>
                            <div class="col-5">
                                <input type="url" class="form-control" name="promotion_btn1_url" value="' . htmlspecialchars($frontendSettings['promotion_btn1_url']) . '" placeholder="https://example.com">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第一个推广按钮的跳转链接</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮2文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_btn2_text" value="' . htmlspecialchars($frontendSettings['promotion_btn2_text']) . '" placeholder="免费网课">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第二个推广按钮的显示文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮2链接</label>
                            </div>
                            <div class="col-5">
                                <input type="url" class="form-control" name="promotion_btn2_url" value="' . htmlspecialchars($frontendSettings['promotion_btn2_url']) . '" placeholder="https://example.com">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第二个推广按钮的跳转链接</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮3文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_btn3_text" value="' . htmlspecialchars($frontendSettings['promotion_btn3_text']) . '" placeholder="官方网站">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第三个推广按钮的显示文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">按钮3链接</label>
                            </div>
                            <div class="col-5">
                                <input type="url" class="form-control" name="promotion_btn3_url" value="' . htmlspecialchars($frontendSettings['promotion_btn3_url']) . '" placeholder="https://example.com">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">第三个推广按钮的跳转链接</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">联系方式文字</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_contact_text" value="' . htmlspecialchars($frontendSettings['promotion_contact_text']) . '" placeholder="唯一售后微信：">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">联系方式的描述文字</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row g-1">
                            <div class="col-auto pe-3" style="min-width: 120px;">
                                <label class="form-label mb-0 d-flex align-items-center h-100">联系方式值</label>
                            </div>
                            <div class="col-5">
                                <input type="text" class="form-control" name="promotion_contact_value" value="' . htmlspecialchars($frontendSettings['promotion_contact_value']) . '" placeholder="hzoedu888">
                            </div>
                            <div class="col">
                                <small class="text-muted d-flex align-items-center h-100">具体的联系方式（微信号等）</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 保存按钮 -->
                <div class="p-4 pt-0 d-flex justify-content-end gap-2">
                    <button type="button" class="btn btn-outline-secondary" onclick="restorePromotionDefaults()">
                        <i class="fas fa-undo"></i> 恢复默认
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveFrontendSettings()">
                        <i class="fas fa-save"></i> 保存设置
                    </button>
                </div>
            </div>
        </div>

        <!-- 弹窗提示 -->
        <div class="tab-pane fade" id="popup" role="tabpanel">
            <div class="p-4">
                <div class="row g-3">
                    <!-- 兑换确认对话框 -->
                    <div class="col-12">
                        <h6 class="text-primary mb-3">🔄 兑换确认对话框</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">确认标题</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="exchange_confirm_title" value="' . htmlspecialchars($frontendSettings['exchange_confirm_title'] ?? '兑换确认') . '" placeholder="兑换确认">
                            </div>
                            <div class="col">
                                <small class="text-muted">兑换确认对话框的标题</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">确认内容</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="exchange_confirm_content" rows="3" placeholder="此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！">' . htmlspecialchars($frontendSettings['exchange_confirm_content'] ?? '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">兑换确认对话框的内容</small>
                            </div>
                        </div>
                    </div>

                    <!-- 验证提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">⚠️ 验证提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">空卡密提示</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="empty_card_message" value="' . htmlspecialchars($frontendSettings['empty_card_message'] ?? '请输入卡密') . '" placeholder="请输入卡密">
                            </div>
                            <div class="col">
                                <small class="text-muted">用户未输入卡密时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 兑换错误提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">❌ 兑换错误提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">卡密不存在</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="card_not_exist_message" value="' . htmlspecialchars($frontendSettings['card_not_exist_message'] ?? '卡密不存在或已失效') . '" placeholder="卡密不存在或已失效">
                            </div>
                            <div class="col">
                                <small class="text-muted">卡密不存在时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">已兑换提示</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="card_already_used_message" rows="2" placeholder="您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！">' . htmlspecialchars($frontendSettings['card_already_used_message'] ?? '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">卡密已兑换时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">卡密被禁用</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="card_disabled_message" value="' . htmlspecialchars($frontendSettings['card_disabled_message'] ?? '卡密已被禁用，请联系微信hzoedu888') . '" placeholder="卡密已被禁用，请联系微信hzoedu888">
                            </div>
                            <div class="col">
                                <small class="text-muted">卡密被禁用时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">卡密过期</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="card_expired_message" value="' . htmlspecialchars($frontendSettings['card_expired_message'] ?? '卡密已过期') . '" placeholder="卡密已过期">
                            </div>
                            <div class="col">
                                <small class="text-muted">卡密过期时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 查询错误提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">🔍 查询错误提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">查询禁用卡密</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="query_disabled_message" rows="2" placeholder="卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888">' . htmlspecialchars($frontendSettings['query_disabled_message'] ?? '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">查询被禁用卡密时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-start g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0 pt-2">未兑换提示</label>
                            </div>
                            <div class="col-sm-6">
                                <textarea class="form-control" name="card_not_exchanged_message" rows="2" placeholder="该卡密尚未兑换，请先兑换后再查看兑换记录">' . htmlspecialchars($frontendSettings['card_not_exchanged_message'] ?? '该卡密尚未兑换，请先兑换后再查看兑换记录') . '</textarea>
                            </div>
                            <div class="col">
                                <small class="text-muted pt-2 d-block">查询未兑换卡密时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">无兑换记录</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="no_exchange_record_message" value="' . htmlspecialchars($frontendSettings['no_exchange_record_message'] ?? '未找到兑换记录') . '" placeholder="未找到兑换记录">
                            </div>
                            <div class="col">
                                <small class="text-muted">未找到兑换记录时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 成功提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">✅ 成功提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">兑换成功</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="exchange_success_message" value="' . htmlspecialchars($frontendSettings['exchange_success_message'] ?? '兑换成功！') . '" placeholder="兑换成功！">
                            </div>
                            <div class="col">
                                <small class="text-muted">兑换成功时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">查询成功</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="query_success_message" value="' . htmlspecialchars($frontendSettings['query_success_message'] ?? '查询成功') . '" placeholder="查询成功">
                            </div>
                            <div class="col">
                                <small class="text-muted">查询成功时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 复制功能提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">📋 复制功能提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">复制成功</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="copy_success_message" value="' . htmlspecialchars($frontendSettings['copy_success_message'] ?? '✅ 链接复制成功！') . '" placeholder="✅ 链接复制成功！">
                            </div>
                            <div class="col">
                                <small class="text-muted">复制成功时的提示</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">复制失败</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="copy_error_message" value="' . htmlspecialchars($frontendSettings['copy_error_message'] ?? '❌ 复制失败，请手动复制') . '" placeholder="❌ 复制失败，请手动复制">
                            </div>
                            <div class="col">
                                <small class="text-muted">复制失败时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 网络错误提示 -->
                    <div class="col-12 mt-4">
                        <h6 class="text-primary mb-3">🌐 网络错误提示</h6>
                    </div>
                    <div class="col-12">
                        <div class="row align-items-center g-2">
                            <div class="col-auto" style="min-width: 120px;">
                                <label class="form-label mb-0">网络错误</label>
                            </div>
                            <div class="col-sm-6">
                                <input type="text" class="form-control" name="network_error_message" value="' . htmlspecialchars($frontendSettings['network_error_message'] ?? '网络错误，请稍后重试') . '" placeholder="网络错误，请稍后重试">
                            </div>
                            <div class="col">
                                <small class="text-muted">网络错误时的提示</small>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="col-12 mt-4">
                        <div class="d-flex justify-content-end gap-2">
                            <button type="button" class="btn btn-outline-secondary" onclick="restoreDefaultMessages()">
                                <i class="fas fa-undo"></i> 恢复默认
                            </button>
                            <button type="button" class="btn btn-primary" onclick="saveFrontendSettings()">
                                <i class="fas fa-save"></i> 保存设置
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
';

?>

<script>
// HTML转义函数，防止XSS攻击
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 保存前端设置
function saveFrontendSettings() {
    const formData = new FormData();

    // 收集通知弹窗数据
    const basicInputs = document.querySelectorAll("#basic input, #basic select, #basic textarea");
    basicInputs.forEach(element => {
        if (element.type === 'checkbox') {
            formData.set(element.name, element.checked ? "1" : "0");
        } else {
            formData.set(element.name, element.value);
        }
    });

    // 收集推广模块数据
    const promotionInputs = document.querySelectorAll("#promotion input, #promotion textarea");
    promotionInputs.forEach(element => {
        if (element.type === 'checkbox') {
            formData.set(element.name, element.checked ? "1" : "0");
        } else {
            formData.set(element.name, element.value);
        }
    });

    // 收集弹窗提示数据
    const popupInputs = document.querySelectorAll("#popup input, #popup textarea");
    popupInputs.forEach(element => {
        formData.set(element.name, element.value);
    });

    // 发送AJAX请求
    fetch('/admin/saveFrontendSettings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 1) {
            showMessage(data.msg, "success");
        } else {
            showMessage(data.msg, "error");
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        showMessage("保存失败，请重试", "error");
    });
}

// 恢复默认提示消息
function restoreDefaultMessages() {
    if (!confirm('确定要恢复所有提示消息为默认值吗？此操作将覆盖当前的自定义设置。')) {
        return;
    }

    // 默认提示消息
    const defaultMessages = {
        // 兑换确认对话框
        'exchange_confirm_title': '兑换确认',
        'exchange_confirm_content': '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！',
        // 验证提示
        'empty_card_message': '请输入卡密',
        // 兑换错误提示
        'card_not_exist_message': '卡密不存在或已失效',
        'card_already_used_message': '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！',
        'card_disabled_message': '卡密已被禁用，请联系微信hzoedu888',
        'card_expired_message': '卡密已过期',
        // 查询错误提示
        'query_disabled_message': '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888',
        'card_not_exchanged_message': '该卡密尚未兑换，请先兑换后再查看兑换记录',
        'no_exchange_record_message': '未找到兑换记录',
        // 成功提示
        'exchange_success_message': '兑换成功！',
        'query_success_message': '查询成功',
        // 复制功能提示
        'copy_success_message': '✅ 链接复制成功！',
        'copy_error_message': '❌ 复制失败，请手动复制',
        // 网络错误提示
        'network_error_message': '网络错误，请稍后重试'
    };

    // 填充表单字段
    for (const [fieldName, defaultValue] of Object.entries(defaultMessages)) {
        const field = document.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.value = defaultValue;
        }
    }

    showMessage('已恢复默认提示消息', 'success');
}

// 恢复推广模块默认设置
function restorePromotionDefaults() {
    if (!confirm('确定要恢复推广模块的默认设置吗？这将覆盖当前的所有推广配置。')) {
        return;
    }

    // 设置默认值
    const defaults = {
        promotion_enabled: false,
        promotion_title: '您还可以点击以下按钮获取更多免费资源',
        promotion_btn1_text: '电子资料包',
        promotion_btn1_url: 'https://example.com/resources',
        promotion_btn2_text: '免费网课',
        promotion_btn2_url: 'https://example.com/courses',
        promotion_btn3_text: '官方网站',
        promotion_btn3_url: 'https://example.com',
        promotion_contact_text: '唯一售后微信：',
        promotion_contact_value: 'hzoedu888'
    };

    // 应用默认值到表单
    document.querySelector('input[name="promotion_enabled"]').checked = defaults.promotion_enabled;
    document.querySelector('input[name="promotion_title"]').value = defaults.promotion_title;
    document.querySelector('input[name="promotion_btn1_text"]').value = defaults.promotion_btn1_text;
    document.querySelector('input[name="promotion_btn1_url"]').value = defaults.promotion_btn1_url;
    document.querySelector('input[name="promotion_btn2_text"]').value = defaults.promotion_btn2_text;
    document.querySelector('input[name="promotion_btn2_url"]').value = defaults.promotion_btn2_url;
    document.querySelector('input[name="promotion_btn3_text"]').value = defaults.promotion_btn3_text;
    document.querySelector('input[name="promotion_btn3_url"]').value = defaults.promotion_btn3_url;
    document.querySelector('input[name="promotion_contact_text"]').value = defaults.promotion_contact_text;
    document.querySelector('input[name="promotion_contact_value"]').value = defaults.promotion_contact_value;

    showMessage('已恢复推广模块默认设置，请点击保存设置以应用更改', 'success');
}

// 备用消息显示函数
function showMessage(message, type = 'info', duration = 3000) {
    // 尝试使用AdminUtils，如果不存在则使用备用方法
    if (window.AdminUtils && window.AdminUtils.showMessage) {
        window.AdminUtils.showMessage(message, type, duration);
        return;
    }

    // 备用方法：使用alert或创建简单的提示
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    };

    // 创建提示元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass[type] || 'alert-info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // 自动移除
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

// 插入HTML标签到通知内容
function insertHtmlTag(openTag, closeTag) {
    const textarea = document.getElementById('notificationContent');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    const newText = openTag + selectedText + closeTag;
    textarea.value = textarea.value.substring(0, start) + newText + textarea.value.substring(end);

    // 重新设置光标位置
    const newCursorPos = start + openTag.length + selectedText.length + closeTag.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();
}

// 插入颜色标签
function insertColorTag(color) {
    const textarea = document.getElementById('notificationContent');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end) || '彩色文字';

    const colorTag = `<span style="color: ${color};">${selectedText}</span>`;
    textarea.value = textarea.value.substring(0, start) + colorTag + textarea.value.substring(end);

    // 重新设置光标位置
    const newCursorPos = start + colorTag.length;
    textarea.setSelectionRange(newCursorPos, newCursorPos);
    textarea.focus();
}

// 预览通知弹窗
function previewNotification() {
    const title = document.querySelector('[name="notification_title"]').value || '系统通知';
    const content = document.querySelector('[name="notification_content"]').value || '暂无内容';
    const style = document.querySelector('[name="notification_style"]:checked')?.value || 'daily';
    const autoClose = parseInt(document.querySelector('[name="notification_auto_close"]').value) || 5;

    let previewHtml = '';

    // 根据不同模板生成不同的预览样式
    switch(style) {
        case 'celebration':
            previewHtml = `
                <div class="modal fade show" style="display: block; position: relative; background: rgba(0,0,0,0.1);">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content border-0 shadow-lg">
                            <div class="modal-header bg-success bg-gradient text-white border-0">
                                <h5 class="modal-title d-flex align-items-center">
                                    <i class="fas fa-trophy me-2"></i>
                                    <span class="me-2">🎉</span>
                                    ${title}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" onclick="closePreview()"></button>
                            </div>
                            <div class="modal-body p-4 text-center">
                                <div class="mb-3">
                                    <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                        <i class="fas fa-star text-white fs-3"></i>
                                    </div>
                                </div>
                                <div class="notification-content">${escapeHtml(content)}</div>
                            </div>
                            <div class="modal-footer border-0 justify-content-center">
                                <button type="button" class="btn btn-success" onclick="closePreview()">
                                    <i class="fas fa-check me-1"></i>知道了
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;

        case 'urgent':
            previewHtml = `
                <div class="modal fade show" style="display: block; position: relative; background: rgba(0,0,0,0.1);">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content border-0 shadow-lg">
                            <div class="modal-header bg-danger bg-gradient text-white border-0">
                                <h5 class="modal-title d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <span class="me-2">⚠️</span>
                                    ${escapeHtml(title)}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" onclick="closePreview()"></button>
                            </div>
                            <div class="modal-body p-4">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        <div class="bg-danger bg-gradient rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            <i class="fas fa-bell text-white fs-5"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="notification-content">${escapeHtml(content)}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer border-0">
                                <button type="button" class="btn btn-outline-danger" onclick="closePreview()">稍后处理</button>
                                <button type="button" class="btn btn-danger" onclick="closePreview()">
                                    <i class="fas fa-check me-1"></i>立即处理
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;

        case 'daily':
        default:
            previewHtml = `
                <div class="modal fade show" style="display: block; position: relative; background: rgba(0,0,0,0.1);">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content border-0 shadow">
                            <div class="modal-header bg-primary bg-gradient text-white border-0">
                                <h5 class="modal-title d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <span class="me-2">📢</span>
                                    ${escapeHtml(title)}
                                </h5>
                                <button type="button" class="btn-close btn-close-white" onclick="closePreview()"></button>
                            </div>
                            <div class="modal-body p-4">
                                <div class="notification-content">${escapeHtml(content)}</div>
                            </div>
                            <div class="modal-footer border-0 justify-content-end">
                                <button type="button" class="btn btn-primary" onclick="closePreview()">
                                    <i class="fas fa-check me-1"></i>确定
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            break;
    }

    // 显示预览
    document.getElementById('previewArea').innerHTML = previewHtml;
    document.getElementById('previewArea').style.display = 'block';

    // 如果设置了自动关闭时间，则启动倒计时
    if (autoClose > 0) {
        let countdown = autoClose;
        const countdownElement = document.createElement('div');
        countdownElement.className = 'text-center mt-2';
        countdownElement.innerHTML = `<small class="text-muted">预览将在 <span class="text-primary fw-bold">${countdown}</span> 秒后自动关闭</small>`;
        document.getElementById('previewArea').appendChild(countdownElement);

        const timer = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                countdownElement.innerHTML = `<small class="text-muted">预览将在 <span class="text-primary fw-bold">${countdown}</span> 秒后自动关闭</small>`;
            } else {
                clearInterval(timer);
                closePreview();
            }
        }, 1000);

        // 存储定时器ID，以便手动关闭时清除
        window.previewTimer = timer;
    }
}

// 关闭预览
function closePreview() {
    // 清除自动关闭定时器
    if (window.previewTimer) {
        clearInterval(window.previewTimer);
        window.previewTimer = null;
    }
    document.getElementById('previewArea').style.display = 'none';
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 监听通知开关变化
    const notificationSwitch = document.getElementById('notificationEnabled');
    if (notificationSwitch) {
        notificationSwitch.addEventListener('change', function() {
            const isEnabled = this.checked;
            const formElements = document.querySelectorAll('[name="notification_title"], [name="notification_content"], [name="notification_style"], [name="notification_auto_close"]');

            formElements.forEach(element => {
                element.disabled = !isEnabled;
                if (!isEnabled) {
                    element.style.opacity = '0.6';
                } else {
                    element.style.opacity = '1';
                }
            });

            // 禁用/启用预览按钮和模板卡片
            const previewBtn = document.querySelector('button[onclick="previewNotification()"]');
            const templateCards = document.querySelectorAll('.notification-template');

            if (previewBtn) {
                previewBtn.disabled = !isEnabled;
                previewBtn.style.opacity = isEnabled ? '1' : '0.6';
            }

            templateCards.forEach(card => {
                card.style.opacity = isEnabled ? '1' : '0.6';
                card.style.pointerEvents = isEnabled ? 'auto' : 'none';
            });
        });

        // 初始化状态
        notificationSwitch.dispatchEvent(new Event('change'));
    }

    // 监听模板卡片点击
    const templateCards = document.querySelectorAll('.notification-template');
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            const template = this.dataset.template;
            const radio = this.querySelector('input[type="radio"]');

            // 选中对应的单选按钮
            radio.checked = true;

            // 更新卡片选中状态
            templateCards.forEach(c => c.classList.remove('border-primary', 'bg-light'));
            this.classList.add('border-primary', 'bg-light');

            // 根据模板类型更新标题建议
            const titleInput = document.querySelector('[name="notification_title"]');
            switch(template) {
                case 'celebration':
                    if (!titleInput.value || titleInput.value === '系统通知') {
                        titleInput.value = '🎉 恭喜您！';
                    }
                    break;
                case 'urgent':
                    if (!titleInput.value || titleInput.value === '系统通知') {
                        titleInput.value = '⚠️ 重要通知';
                    }
                    break;
                case 'daily':
                    if (!titleInput.value || titleInput.value === '系统通知') {
                        titleInput.value = '📢 系统通知';
                    }
                    break;
            }
        });
    });

    // 初始化选中状态
    const checkedRadio = document.querySelector('[name="notification_style"]:checked');
    if (checkedRadio) {
        const selectedCard = document.querySelector(`[data-template="${checkedRadio.value}"]`);
        if (selectedCard) {
            selectedCard.classList.add('border-primary', 'bg-light');
        }
    }
});
</script>

<?php
include 'layout.php';
?>
