-- 卡密兑换系统数据库初始化脚本
-- 数据库：731kmxt

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `731kmxt` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `731kmxt`;

-- 创建分类表（支持三级分类）
CREATE TABLE IF NOT EXISTS `categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `parent_id` int(11) DEFAULT 0 COMMENT '父级分类ID，0为顶级分类',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `level` tinyint(1) DEFAULT 1 COMMENT '分类层级：1-3级',
  `path` varchar(500) DEFAULT NULL COMMENT '分类路径，如：1,2,3',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资料分类表';

-- 创建内容表
CREATE TABLE IF NOT EXISTS `contents` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '内容ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `title` varchar(200) NOT NULL COMMENT '内容标题',
  `description` text COMMENT '内容描述',
  `content` longtext COMMENT '详细内容',
  `file_path` varchar(500) COMMENT '文件路径',
  `file_name` varchar(200) COMMENT '原始文件名',
  `file_size` bigint(20) DEFAULT 0 COMMENT '文件大小（字节）',
  `file_type` varchar(50) COMMENT '文件类型',
  `cover_image` varchar(500) COMMENT '封面图片',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐：1是，0否',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status_sort` (`status`, `sort_order`),
  KEY `idx_featured` (`is_featured`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资料内容表';

-- 创建卡密表
CREATE TABLE IF NOT EXISTS `cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '卡密ID',
  `card_number` varchar(50) NOT NULL COMMENT '卡密号码',
  `card_type` varchar(50) NOT NULL COMMENT '卡密类型',
  `batch_id` varchar(50) DEFAULT NULL COMMENT '批次ID',
  `category_id` int(11) DEFAULT NULL COMMENT '关联分类ID',
  `content_ids` text COMMENT '可兑换内容ID列表（JSON格式）',
  `value` decimal(10,2) DEFAULT 0.00 COMMENT '卡密面值',
  `valid_days` int(11) DEFAULT 0 COMMENT '有效天数，0为永久',
  `max_use_count` int(11) DEFAULT 1 COMMENT '最大使用次数',
  `used_count` int(11) DEFAULT 0 COMMENT '已使用次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1未使用，2已使用，0已禁用',
  `used_time` datetime DEFAULT NULL COMMENT '首次使用时间',
  `used_ip` varchar(45) DEFAULT NULL COMMENT '使用IP',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_number` (`card_number`),
  KEY `idx_status` (`status`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='卡密表';

-- 创建兑换记录表
CREATE TABLE IF NOT EXISTS `exchange_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `card_id` int(11) NOT NULL COMMENT '卡密ID',
  `card_number` varchar(50) NOT NULL COMMENT '卡密号码',
  `content_ids` text COMMENT '兑换的内容ID列表（JSON格式）',
  `exchange_ip` varchar(45) DEFAULT NULL COMMENT '兑换IP',
  `user_agent` text COMMENT '用户代理',
  `exchange_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '兑换时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1成功，0失败',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_card_id` (`card_id`),
  KEY `idx_card_number` (`card_number`),
  KEY `idx_exchange_time` (`exchange_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='兑换记录表';

-- 创建管理员表
CREATE TABLE IF NOT EXISTS `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码哈希',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role` varchar(50) DEFAULT 'admin' COMMENT '角色',
  `permissions` text DEFAULT NULL COMMENT '权限列表（JSON格式）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';



-- 插入默认管理员账号 admin/123456
INSERT INTO `admin_users` (`username`, `password`, `nickname`, `role`, `status`) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统管理员', 'admin', 1);

-- 插入测试分类数据（三级分类结构）
INSERT INTO `categories` (`id`, `parent_id`, `name`, `description`, `icon`, `sort_order`, `level`, `path`, `status`) VALUES
(1, 0, '网络安全', '网络安全相关资料', 'fas fa-shield-alt', 1, 1, '1', 1),
(2, 1, '渗透测试', '渗透测试技术资料', 'fas fa-bug', 1, 2, '1,2', 1),
(3, 1, '安全防护', '网络安全防护资料', 'fas fa-lock', 2, 2, '1,3', 1),
(4, 0, '编程开发', '编程开发相关资料', 'fas fa-code', 2, 1, '4', 1),
(5, 4, 'Web开发', 'Web前后端开发', 'fas fa-globe', 1, 2, '4,5', 1),
(6, 4, '移动开发', '移动应用开发', 'fas fa-mobile-alt', 2, 2, '4,6', 1),
(7, 5, 'PHP开发', 'PHP后端开发', 'fab fa-php', 1, 3, '4,5,7', 1),
(8, 5, 'JavaScript', 'JavaScript前端开发', 'fab fa-js', 2, 3, '4,5,8', 1),
(9, 0, '系统运维', '系统运维相关资料', 'fas fa-server', 3, 1, '9', 1),
(10, 0, '数据分析', '数据分析相关资料', 'fas fa-chart-bar', 4, 1, '10', 1);

-- 插入测试内容数据
INSERT INTO `contents` (`category_id`, `title`, `description`, `content`, `file_path`, `file_name`, `file_size`, `file_type`, `cover_image`, `sort_order`, `is_featured`, `status`) VALUES
(2, '网络安全基础教程', '网络安全入门必备教程，涵盖基础概念和实践技巧', '详细的网络安全基础知识内容...', '/uploads/security_basic.pdf', 'security_basic.pdf', 1024000, 'pdf', '/uploads/covers/security_basic.jpg', 1, 1, 1),
(2, '渗透测试实战指南', '实战渗透测试技巧分享，包含多个真实案例', '渗透测试实战案例和技巧详解...', '/uploads/pentest_guide.pdf', 'pentest_guide.pdf', 2048000, 'pdf', '/uploads/covers/pentest_guide.jpg', 2, 1, 1),
(3, '网络防火墙配置', '企业级防火墙配置与管理指南', '防火墙配置的详细步骤和最佳实践...', '/uploads/firewall_config.pdf', 'firewall_config.pdf', 1536000, 'pdf', '/uploads/covers/firewall_config.jpg', 1, 0, 1),
(7, 'PHP开发实战', 'PHP Web开发完整教程，从入门到精通', 'PHP开发的完整学习路径和实战项目...', '/uploads/php_dev.pdf', 'php_dev.pdf', 3072000, 'pdf', '/uploads/covers/php_dev.jpg', 1, 1, 1),
(8, 'JavaScript高级编程', 'JavaScript进阶技术详解，ES6+新特性', 'JavaScript高级特性和现代开发技巧...', '/uploads/js_advanced.pdf', 'js_advanced.pdf', 2560000, 'pdf', '/uploads/covers/js_advanced.jpg', 2, 1, 1),
(9, 'Linux服务器运维', 'Linux系统管理和服务器运维指南', 'Linux服务器的日常运维和故障排除...', '/uploads/linux_ops.pdf', 'linux_ops.pdf', 2048000, 'pdf', '/uploads/covers/linux_ops.jpg', 1, 0, 1);

-- 插入测试卡密数据
INSERT INTO `cards` (`card_number`, `card_type`, `batch_id`, `category_id`, `content_ids`, `value`, `valid_days`, `max_use_count`, `status`, `expire_time`, `remark`) VALUES
('XXXX-XXXX-XXXX-1234', '网络安全专享', 'BATCH001', 1, '[1,2,3]', 99.00, 365, 1, 1, '2025-12-31 23:59:59', '网络安全分类专用卡密'),
('XXXX-XXXX-XXXX-5678', '月度会员', 'BATCH002', NULL, '[1,2,3,4,5,6]', 199.00, 30, 3, 1, '2025-12-31 23:59:59', '月度会员卡密，可使用3次'),
('XXXX-XXXX-XXXX-9012', '编程开发', 'BATCH001', 4, '[4,5]', 149.00, 180, 1, 1, '2025-12-31 23:59:59', '编程开发分类专用'),
('XXXX-XXXX-XXXX-3456', '高级会员', 'BATCH003', NULL, '[1,2,3,4,5,6]', 299.00, 365, 5, 2, '2025-12-31 23:59:59', '高级会员卡密（已使用）'),
('XXXX-XXXX-XXXX-7890', '系统运维', 'BATCH001', 9, '[6]', 79.00, 90, 1, 1, '2025-12-31 23:59:59', '系统运维专用卡密');


