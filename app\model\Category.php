<?php

namespace app\model;

use think\Model;

class Category extends Model
{
    protected $table = 'categories';
    
    // 设置字段信息
    protected $schema = [
        'id'          => 'int',
        'parent_id'   => 'int',
        'name'        => 'string',
        'description' => 'string',
        'icon'        => 'string',
        'sort_order'  => 'int',
        'level'       => 'int',
        'path'        => 'string',
        'status'      => 'int',
        'create_time' => 'datetime',
        'update_time' => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 获取启用的分类
     */
    public static function getEnabled()
    {
        return self::where('status', self::STATUS_ENABLED)
                   ->order('sort_order', 'asc')
                   ->order('id', 'asc')
                   ->select();
    }

    /**
     * 获取树形结构的分类
     * 只返回启用状态的分类，且父分类也必须是启用状态
     */
    public static function getTree()
    {
        $categories = self::getAllForDisplay();
        return self::buildTree($categories);
    }

    /**
     * 构建树形结构
     */
    private static function buildTree($categories, $parentId = 0)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = self::buildTree($categories, $category['id']);
                $category['has_children'] = !empty($category['children']);
                $tree[] = $category;
            }
        }
        return $tree;
    }

    /**
     * 获取所有分类（扁平结构，用于显示）
     * 只返回启用状态的分类，且父分类也必须是启用状态
     */
    public static function getAllForDisplay()
    {
        $allCategories = self::where('status', self::STATUS_ENABLED)
                            ->order('sort_order', 'asc')
                            ->order('id', 'asc')
                            ->select()
                            ->toArray();

        // 过滤掉父分类被禁用的分类
        return self::filterEnabledCategories($allCategories);
    }

    /**
     * 过滤出有效的分类（父分类链都必须是启用状态）
     */
    private static function filterEnabledCategories($categories)
    {
        $validCategories = [];
        $categoryMap = [];

        // 创建分类映射
        foreach ($categories as $category) {
            $categoryMap[$category['id']] = $category;
        }

        foreach ($categories as $category) {
            if (self::isCategoryChainEnabled($category, $categoryMap)) {
                $validCategories[] = $category;
            }
        }

        return $validCategories;
    }

    /**
     * 检查分类链是否都是启用状态
     */
    private static function isCategoryChainEnabled($category, $categoryMap)
    {
        // 如果是顶级分类，直接返回其状态
        if ($category['parent_id'] == 0) {
            return $category['status'] == self::STATUS_ENABLED;
        }

        // 检查父分类是否存在且启用
        if (!isset($categoryMap[$category['parent_id']])) {
            return false;
        }

        $parent = $categoryMap[$category['parent_id']];

        // 递归检查父分类链
        return $parent['status'] == self::STATUS_ENABLED &&
               self::isCategoryChainEnabled($parent, $categoryMap);
    }

    /**
     * 获取所有分类（用于管理后台，包括禁用的）
     * 按层级结构排序：先按parent_id分组，再按sort_order排序
     */
    public static function getAllForAdmin()
    {
        $categories = self::order('parent_id', 'asc')
                         ->order('sort_order', 'asc')
                         ->order('id', 'asc')
                         ->select()
                         ->toArray();

        return self::buildHierarchicalList($categories);
    }

    /**
     * 构建层级排序的分类列表
     */
    private static function buildHierarchicalList($categories, $parentId = 0, &$result = [])
    {
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $result[] = $category;
                // 递归添加子分类
                self::buildHierarchicalList($categories, $category['id'], $result);
            }
        }
        return $result;
    }

    /**
     * 获取树形结构的分类（用于管理后台，包括禁用的）
     */
    public static function getTreeForAdmin()
    {
        $categories = self::order('sort_order', 'asc')
                         ->order('id', 'asc')
                         ->select()
                         ->toArray();

        return self::buildTree($categories);
    }

    /**
     * 检查是否有子分类
     */
    public function hasChildren()
    {
        return self::where('parent_id', $this->id)->count() > 0;
    }

    /**
     * 获取子分类
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    /**
     * 获取父分类
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    /**
     * 关联内容
     */
    public function contents()
    {
        return $this->hasMany(Content::class, 'category_id', 'id');
    }

    /**
     * 判断是否为叶子节点（最低级分类）
     */
    public function isLeafNode()
    {
        return $this->children()->count() == 0;
    }

    /**
     * 获取所有叶子节点分类（可以添加内容的分类）
     */
    public static function getLeafCategories()
    {
        $allCategories = self::getAllForAdmin();
        $leafCategories = [];

        foreach ($allCategories as $category) {
            if ($category['status'] == 1) { // 只处理启用的分类
                $categoryModel = self::find($category['id']);
                if ($categoryModel && $categoryModel->isLeafNode()) {
                    $leafCategories[] = $category;
                }
            }
        }

        return $leafCategories;
    }

    /**
     * 获取指定分类及其所有子分类的ID数组
     */
    public static function getCategoryAndChildrenIds($categoryId)
    {
        if (empty($categoryId)) {
            return [];
        }

        $allCategories = self::getAllForAdmin();
        $categoryIds = [$categoryId]; // 包含自己

        // 递归获取所有子分类ID
        self::getChildrenIds($allCategories, $categoryId, $categoryIds);

        return $categoryIds;
    }

    /**
     * 递归获取子分类ID
     */
    private static function getChildrenIds($allCategories, $parentId, &$categoryIds)
    {
        foreach ($allCategories as $category) {
            if ($category['parent_id'] == $parentId) {
                $categoryIds[] = $category['id'];
                // 递归获取子分类的子分类
                self::getChildrenIds($allCategories, $category['id'], $categoryIds);
            }
        }
    }
}
