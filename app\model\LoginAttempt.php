<?php

namespace app\model;

use think\Model;

class LoginAttempt extends Model
{
    protected $table = 'login_attempts';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'username'       => 'string',
        'ip_address'     => 'string',
        'user_agent'     => 'string',
        'attempt_time'   => 'datetime',
        'status'         => 'int',
        'failure_reason' => 'string',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'attempt_time';
    
    // 状态常量
    const STATUS_FAILED = 0;   // 失败
    const STATUS_SUCCESS = 1;  // 成功
    
    /**
     * 记录登录尝试
     */
    public static function recordAttempt($username, $status, $failureReason = null)
    {
        return self::create([
            'username' => $username,
            'ip_address' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'status' => $status,
            'failure_reason' => $failureReason,
        ]);
    }
    
    /**
     * 获取指定时间内的失败次数
     */
    public static function getFailureCount($username, $minutes = 30)
    {
        $timeLimit = date('Y-m-d H:i:s', time() - ($minutes * 60));
        
        return self::where('username', $username)
                   ->where('status', self::STATUS_FAILED)
                   ->where('attempt_time', '>', $timeLimit)
                   ->count();
    }
    
    /**
     * 获取指定IP的失败次数
     */
    public static function getIpFailureCount($ipAddress, $minutes = 30)
    {
        $timeLimit = date('Y-m-d H:i:s', time() - ($minutes * 60));
        
        return self::where('ip_address', $ipAddress)
                   ->where('status', self::STATUS_FAILED)
                   ->where('attempt_time', '>', $timeLimit)
                   ->count();
    }
    
    /**
     * 清理过期记录
     */
    public static function cleanExpiredRecords($days = 30)
    {
        $timeLimit = date('Y-m-d H:i:s', time() - ($days * 24 * 60 * 60));
        
        return self::where('attempt_time', '<', $timeLimit)->delete();
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_FAILED => '失败',
            self::STATUS_SUCCESS => '成功',
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }
}
