# 卡密兑换系统生产环境部署指南

## 📋 目录
- [环境要求](#环境要求)
- [服务器准备](#服务器准备)
- [代码部署](#代码部署)
- [数据库配置](#数据库配置)
- [Web服务器配置](#web服务器配置)
- [安全配置](#安全配置)
- [性能优化](#性能优化)
- [监控与维护](#监控与维护)
- [常见问题](#常见问题)

## 🔧 环境要求

### 基础环境
- **操作系统**: Linux (推荐 Ubuntu 20.04+ 或 CentOS 8+)
- **PHP版本**: PHP 8.1+ (推荐 8.2)
- **Web服务器**: Nginx 1.18+ 或 Apache 2.4+
- **数据库**: MySQL 8.0+ 或 MariaDB 10.6+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 20GB，推荐 50GB+

### PHP扩展要求
```bash
# 必需扩展
php-fpm
php-mysql
php-json
php-mbstring
php-xml
php-curl
php-zip
php-gd
php-fileinfo
php-openssl

# 可选扩展（性能优化）
php-opcache
php-redis
php-memcached
```

## 🖥️ 服务器准备

### 1. 更新系统
```bash
# Ubuntu/Debian
sudo apt update && sudo apt upgrade -y

# CentOS/RHEL
sudo yum update -y
```

### 2. 安装基础软件
```bash
# Ubuntu/Debian
sudo apt install -y nginx mysql-server php8.2-fpm php8.2-mysql php8.2-json \
php8.2-mbstring php8.2-xml php8.2-curl php8.2-zip php8.2-gd php8.2-fileinfo \
php8.2-openssl php8.2-opcache composer git unzip

# CentOS/RHEL (需要先安装EPEL和Remi仓库)
sudo yum install -y epel-release
sudo yum install -y https://rpms.remirepo.net/enterprise/remi-release-8.rpm
sudo yum module enable php:remi-8.2 -y
sudo yum install -y nginx mysql-server php-fpm php-mysql php-json \
php-mbstring php-xml php-curl php-zip php-gd php-fileinfo \
php-openssl php-opcache composer git unzip
```

### 3. 启动服务
```bash
# 启动并设置开机自启
sudo systemctl start nginx mysql php8.2-fpm
sudo systemctl enable nginx mysql php8.2-fpm

# 检查服务状态
sudo systemctl status nginx mysql php8.2-fpm
```

## 📦 代码部署

### 1. 创建项目目录
```bash
# 创建网站根目录
sudo mkdir -p /var/www/kmxt
sudo chown -R www-data:www-data /var/www/kmxt
cd /var/www/kmxt
```

### 2. 上传代码
```bash
# 方式1: 使用Git (推荐)
git clone <你的仓库地址> .

# 方式2: 直接上传文件
# 将本地项目文件上传到 /var/www/kmxt 目录

# 设置权限
sudo chown -R www-data:www-data /var/www/kmxt
sudo chmod -R 755 /var/www/kmxt
sudo chmod -R 777 /var/www/kmxt/runtime
sudo chmod -R 777 /var/www/kmxt/public/uploads
```

### 3. 安装依赖
```bash
cd /var/www/kmxt
composer install --no-dev --optimize-autoloader
```

## 🗄️ 数据库配置

### 1. 创建数据库和用户
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE kmxt_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'kmxt_user'@'localhost' IDENTIFIED BY '你的强密码';

-- 授权
GRANT ALL PRIVILEGES ON kmxt_prod.* TO 'kmxt_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

### 2. 导入数据库结构
```bash
# 导入数据库结构（假设你有SQL文件）
mysql -u kmxt_user -p kmxt_prod < database.sql
```

### 3. 修改数据库配置
编辑 `config/database.php`:
```php
<?php
return [
    'default' => 'mysql',
    'connections' => [
        'mysql' => [
            'type'            => 'mysql',
            'hostname'        => 'localhost',
            'database'        => 'kmxt_prod',
            'username'        => 'kmxt_user',
            'password'        => '你的强密码',
            'hostport'        => '3306',
            'params'          => [],
            'charset'         => 'utf8mb4',
            'prefix'          => '',
            'debug'           => false,
            'deploy'          => 0,
            'rw_separate'     => false,
            'master_num'      => 1,
            'slave_no'        => '',
            'fields_strict'   => true,
            'result_type'     => \PDO::FETCH_ASSOC,
            'resultset_type'  => 'array',
            'auto_timestamp'  => false,
            'datetime_format' => 'Y-m-d H:i:s',
            'sql_explain'     => false,
        ],
    ],
];
```

## 🌐 Web服务器配置

### Nginx配置
创建 `/etc/nginx/sites-available/kmxt`:
```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    root /var/www/kmxt/public;
    index index.php index.html;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 隐藏Nginx版本
    server_tokens off;

    # 文件上传大小限制
    client_max_body_size 20M;

    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 主要路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }

    location ~ /(config|runtime|vendor)/ {
        deny all;
    }

    # 日志
    access_log /var/log/nginx/kmxt_access.log;
    error_log /var/log/nginx/kmxt_error.log;
}
```

启用站点:
```bash
sudo ln -s /etc/nginx/sites-available/kmxt /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### SSL证书配置 (推荐)
```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔒 安全配置

### 1. 修改应用配置
编辑 `config/app.php`:
```php
<?php
return [
    // 应用调试模式
    'app_debug' => false,

    // 应用Trace
    'app_trace' => false,

    // 应用域名
    'app_host' => 'your-domain.com',

    // 应用地址
    'app_url' => 'https://your-domain.com',

    // 入口自动绑定模块
    'auto_bind_module' => false,

    // 默认跳转页面对应的模板文件
    'default_return_type' => 'html',

    // 默认AJAX 数据返回格式
    'default_ajax_return' => 'json',

    // 异常页面的模板文件
    'exception_tmpl' => app()->getThinkPath() . 'tpl/think_exception.tpl',

    // 错误显示信息
    'error_message' => '页面错误！请稍后再试～',

    // 显示错误信息
    'show_error_msg' => false,
];
```

### 2. 设置环境变量
创建 `.env` 文件:
```env
APP_DEBUG=false
APP_TRACE=false

[DATABASE]
TYPE=mysql
HOSTNAME=localhost
DATABASE=kmxt_prod
USERNAME=kmxt_user
PASSWORD=你的强密码
HOSTPORT=3306
CHARSET=utf8mb4
DEBUG=false

[SECURITY]
SESSION_SECURE=true
SESSION_HTTPONLY=true
CSRF_TOKEN=true
```

### 3. 文件权限设置
```bash
# 设置正确的文件权限
sudo chown -R www-data:www-data /var/www/kmxt
sudo find /var/www/kmxt -type d -exec chmod 755 {} \;
sudo find /var/www/kmxt -type f -exec chmod 644 {} \;
sudo chmod -R 777 /var/www/kmxt/runtime
sudo chmod -R 777 /var/www/kmxt/public/uploads

# 保护敏感文件
sudo chmod 600 /var/www/kmxt/.env
sudo chmod 600 /var/www/kmxt/config/database.php
```

### 4. 防火墙配置
```bash
# Ubuntu UFW
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# CentOS Firewalld
sudo systemctl enable firewalld
sudo systemctl start firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 5. MySQL安全配置
```bash
# 运行MySQL安全脚本
sudo mysql_secure_installation

# 配置MySQL用户权限
mysql -u root -p
```

```sql
-- 删除匿名用户
DELETE FROM mysql.user WHERE User='';

-- 删除测试数据库
DROP DATABASE IF EXISTS test;

-- 禁用root远程登录
DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');

-- 刷新权限
FLUSH PRIVILEGES;
```

## ⚡ 性能优化

### 1. PHP-FPM优化
编辑 `/etc/php/8.2/fpm/pool.d/www.conf`:
```ini
; 进程管理方式
pm = dynamic

; 最大子进程数
pm.max_children = 50

; 启动时的进程数
pm.start_servers = 5

; 最小空闲进程数
pm.min_spare_servers = 5

; 最大空闲进程数
pm.max_spare_servers = 35

; 每个子进程处理的最大请求数
pm.max_requests = 500
```

### 2. PHP配置优化
编辑 `/etc/php/8.2/fpm/php.ini`:
```ini
; 内存限制
memory_limit = 256M

; 执行时间限制
max_execution_time = 60

; 文件上传大小
upload_max_filesize = 20M
post_max_size = 20M

; OPcache配置
opcache.enable=1
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000
opcache.revalidate_freq=2
opcache.fast_shutdown=1

; 会话配置
session.save_handler = files
session.save_path = "/var/lib/php/sessions"
session.gc_maxlifetime = 1440
session.cookie_secure = 1
session.cookie_httponly = 1
```

### 3. MySQL优化
编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf`:
```ini
[mysqld]
# 基础配置
max_connections = 200
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# 查询缓存
query_cache_type = 1
query_cache_size = 64M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log_bin = /var/log/mysql/mysql-bin.log
expire_logs_days = 7
max_binlog_size = 100M
```

### 4. Nginx优化
编辑 `/etc/nginx/nginx.conf`:
```nginx
user www-data;
worker_processes auto;
worker_connections 1024;

# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 缓冲区设置
client_body_buffer_size 128k;
client_max_body_size 20m;
client_header_buffer_size 1k;
large_client_header_buffers 4 4k;
output_buffers 1 32k;
postpone_output 1460;

# 超时设置
client_header_timeout 3m;
client_body_timeout 3m;
send_timeout 3m;

# 连接设置
keepalive_timeout 65;
keepalive_requests 100;
```

重启服务:
```bash
sudo systemctl restart php8.2-fpm nginx mysql
```

## 📊 监控与维护

### 1. 日志监控
```bash
# 创建日志监控脚本
sudo nano /usr/local/bin/log_monitor.sh
```

```bash
#!/bin/bash
# 日志监控脚本

LOG_DIR="/var/log"
NGINX_ERROR_LOG="$LOG_DIR/nginx/kmxt_error.log"
PHP_ERROR_LOG="$LOG_DIR/php8.2-fpm.log"
MYSQL_ERROR_LOG="$LOG_DIR/mysql/error.log"

# 检查错误日志
echo "=== Nginx错误日志 ==="
tail -n 20 $NGINX_ERROR_LOG

echo "=== PHP错误日志 ==="
tail -n 20 $PHP_ERROR_LOG

echo "=== MySQL错误日志 ==="
tail -n 20 $MYSQL_ERROR_LOG

# 检查磁盘空间
echo "=== 磁盘使用情况 ==="
df -h

# 检查内存使用
echo "=== 内存使用情况 ==="
free -h

# 检查服务状态
echo "=== 服务状态 ==="
systemctl status nginx php8.2-fpm mysql --no-pager
```

```bash
# 设置执行权限
sudo chmod +x /usr/local/bin/log_monitor.sh

# 设置定时任务
sudo crontab -e
# 添加以下行（每小时检查一次）
0 * * * * /usr/local/bin/log_monitor.sh >> /var/log/system_monitor.log 2>&1
```

### 2. 数据库备份
```bash
# 创建备份脚本
sudo nano /usr/local/bin/db_backup.sh
```

```bash
#!/bin/bash
# 数据库备份脚本

DB_NAME="kmxt_prod"
DB_USER="kmxt_user"
DB_PASS="你的强密码"
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/kmxt_backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/kmxt_backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "kmxt_backup_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: kmxt_backup_$DATE.sql.gz"
```

```bash
# 设置执行权限
sudo chmod +x /usr/local/bin/db_backup.sh

# 设置定时任务（每天凌晨2点备份）
sudo crontab -e
# 添加以下行
0 2 * * * /usr/local/bin/db_backup.sh >> /var/log/db_backup.log 2>&1
```

### 3. 系统监控
安装监控工具:
```bash
# 安装htop和iotop
sudo apt install htop iotop

# 安装Netdata (可选的Web监控面板)
bash <(curl -Ss https://my-netdata.io/kickstart.sh)
```

### 4. 日志轮转
创建 `/etc/logrotate.d/kmxt`:
```
/var/log/nginx/kmxt_*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        if [ -f /var/run/nginx.pid ]; then
            kill -USR1 `cat /var/run/nginx.pid`
        fi
    endscript
}
```

### 5. 更新维护
```bash
# 创建更新脚本
sudo nano /usr/local/bin/system_update.sh
```

```bash
#!/bin/bash
# 系统更新脚本

echo "开始系统更新..."

# 更新包列表
apt update

# 升级系统包
apt upgrade -y

# 清理不需要的包
apt autoremove -y
apt autoclean

# 重启服务
systemctl restart nginx php8.2-fpm

echo "系统更新完成"
```

## ❓ 常见问题

### 1. 500内部服务器错误
**问题**: 访问网站显示500错误

**解决方案**:
```bash
# 检查Nginx错误日志
sudo tail -f /var/log/nginx/kmxt_error.log

# 检查PHP-FPM错误日志
sudo tail -f /var/log/php8.2-fpm.log

# 检查文件权限
sudo chown -R www-data:www-data /var/www/kmxt
sudo chmod -R 755 /var/www/kmxt
sudo chmod -R 777 /var/www/kmxt/runtime

# 检查PHP-FPM状态
sudo systemctl status php8.2-fpm
```

### 2. 数据库连接失败
**问题**: 无法连接到数据库

**解决方案**:
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 测试数据库连接
mysql -u kmxt_user -p kmxt_prod

# 检查数据库配置文件
cat /var/www/kmxt/config/database.php

# 检查防火墙设置
sudo ufw status
```

### 3. 文件上传失败
**问题**: 无法上传文件

**解决方案**:
```bash
# 检查上传目录权限
sudo chmod -R 777 /var/www/kmxt/public/uploads

# 检查PHP配置
php -i | grep upload_max_filesize
php -i | grep post_max_size

# 检查Nginx配置
grep client_max_body_size /etc/nginx/sites-available/kmxt
```

### 4. 性能问题
**问题**: 网站响应缓慢

**解决方案**:
```bash
# 检查系统资源
htop
iotop

# 检查MySQL慢查询
sudo tail -f /var/log/mysql/slow.log

# 启用OPcache
php -m | grep OPcache

# 检查Nginx访问日志
sudo tail -f /var/log/nginx/kmxt_access.log
```

### 5. SSL证书问题
**问题**: HTTPS无法访问

**解决方案**:
```bash
# 检查证书状态
sudo certbot certificates

# 手动续期证书
sudo certbot renew

# 检查Nginx SSL配置
sudo nginx -t

# 重新获取证书
sudo certbot --nginx -d your-domain.com
```

## ✅ 部署检查清单

### 部署前检查
- [ ] 服务器环境满足要求
- [ ] 域名已解析到服务器IP
- [ ] 数据库已创建并配置
- [ ] SSL证书已申请（如需要）

### 代码部署检查
- [ ] 代码已上传到服务器
- [ ] Composer依赖已安装
- [ ] 文件权限已正确设置
- [ ] 配置文件已修改

### 服务配置检查
- [ ] Nginx配置已创建并启用
- [ ] PHP-FPM配置已优化
- [ ] MySQL配置已优化
- [ ] 防火墙规则已设置

### 安全配置检查
- [ ] 调试模式已关闭
- [ ] 敏感文件权限已设置
- [ ] 数据库用户权限已限制
- [ ] SSL证书已配置（如需要）

### 功能测试检查
- [ ] 网站首页可正常访问
- [ ] 管理后台可正常登录
- [ ] 数据库连接正常
- [ ] 文件上传功能正常
- [ ] 卡密生成功能正常
- [ ] 内容管理功能正常

### 监控配置检查
- [ ] 日志监控脚本已设置
- [ ] 数据库备份脚本已设置
- [ ] 系统监控工具已安装
- [ ] 日志轮转已配置

## 🚀 部署完成后的操作

### 1. 创建管理员账户
访问网站并创建第一个管理员账户

### 2. 配置系统设置
- 设置网站基本信息
- 配置邮件发送设置
- 设置文件上传限制

### 3. 创建分类结构
- 创建内容分类
- 设置分类权限

### 4. 测试核心功能
- 测试卡密生成
- 测试内容管理
- 测试导入导出功能

### 5. 性能测试
- 使用压力测试工具测试网站性能
- 监控服务器资源使用情况

---

## 📞 技术支持

如果在部署过程中遇到问题，请检查：
1. 系统日志：`/var/log/syslog`
2. Nginx日志：`/var/log/nginx/`
3. PHP日志：`/var/log/php8.2-fpm.log`
4. MySQL日志：`/var/log/mysql/`

**注意**: 请根据实际情况修改配置文件中的域名、密码等信息。建议在测试环境中先完整测试部署流程，确认无误后再在生产环境中部署。