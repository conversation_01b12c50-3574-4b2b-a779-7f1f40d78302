<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <?php
    // 构建页面标题
    $pageTitle = htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育');
    if (!empty($settings['site_subtitle'])) {
        $pageTitle .= ' - ' . htmlspecialchars($settings['site_subtitle']);
    }
    ?>
    <title><?php echo $pageTitle; ?></title>

    <!-- SEO Meta标签 -->
    <meta name="description" content="<?php echo htmlspecialchars($settings['site_description'] ?? '电子资料兑换系统'); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($settings['site_keywords'] ?? '卡密,兑换,资料,教程'); ?>">
    <meta name="author" content="<?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?>">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <link href="/static/css/user.css" rel="stylesheet">
    <link href="/static/css/query-modal.css" rel="stylesheet">
    <link href="/static/css/alert-modal.css" rel="stylesheet">

    <style>
        /* 通知弹窗样式 */
        #notificationModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
        }

        #notificationModal .modal-dialog {
            margin: 1.75rem auto;
            max-width: 500px;
        }

        .notification-content {
            line-height: 1.6;
        }

        .notification-content p {
            margin-bottom: 1rem;
        }

        .notification-content p:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Logo和副标题区域 -->
        <div class="brand-section">
            <?php if (!empty($settings['site_logo'])): ?>
            <div class="logo-container">
                <img src="<?php echo htmlspecialchars($settings['site_logo']); ?>" alt="<?php echo htmlspecialchars($settings['site_name'] ?? '红嘴鸥教育'); ?>" class="site-logo">
            </div>
            <?php endif; ?>

            <?php if (!empty($settings['site_subtitle'])): ?>
            <div class="site-subtitle"><?php echo htmlspecialchars($settings['site_subtitle']); ?></div>
            <?php endif; ?>
        </div>
        
        <div class="input-flex">
            <input type="text" id="cardNumber" placeholder="请输入卡密" />
            <button onclick="exchange()">立即兑换</button>
        </div>

        <button onclick="showQueryModal()" class="query-button">兑换记录查询</button>

        <div id="result" class="result-area"></div>
        
        <div class="note">
            <strong>说明：</strong> <?php echo htmlspecialchars($settings['page_footer_notice'] ?? '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888'); ?>
        </div>
    </div>

    <!-- 悬浮联系客服模块 -->
    <?php if (!empty($settings['contact_qq']) || !empty($settings['contact_wechat']) || !empty($settings['contact_email']) || !empty($settings['contact_phone']) || !empty($settings['work_time'])): ?>
    <div class="floating-contact-container">
        <!-- PC端悬停卡片 -->
        <div class="contact-hover-card">
            <div class="contact-card-header">
                <i class="fas fa-headset"></i>
                <span>联系客服</span>
            </div>
            <div class="contact-card-body">
                <?php if (!empty($settings['contact_wechat'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_wechat']); ?>', '微信')">
                    <i class="fab fa-weixin"></i>
                    <span><?php echo htmlspecialchars($settings['contact_wechat']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_qq'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_qq']); ?>', 'QQ')">
                    <i class="fab fa-qq"></i>
                    <span><?php echo htmlspecialchars($settings['contact_qq']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_email'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_email']); ?>', '邮箱')">
                    <i class="fas fa-envelope"></i>
                    <span><?php echo htmlspecialchars($settings['contact_email']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_phone'])): ?>
                <div class="contact-card-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_phone']); ?>', '电话')">
                    <i class="fas fa-phone"></i>
                    <span><?php echo htmlspecialchars($settings['contact_phone']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['work_time'])): ?>
                <div class="contact-card-item">
                    <i class="fas fa-clock"></i>
                    <span><?php echo htmlspecialchars($settings['work_time']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 悬浮按钮 -->
        <div class="floating-contact-btn" onclick="handleContactClick()">
            <i class="fas fa-comments"></i>
        </div>
    </div>

    <!-- 移动端弹窗 -->
    <div id="contactModal" class="contact-modal">
        <div class="contact-modal-content">
            <div class="contact-modal-header">
                <h4>
                    <i class="fas fa-headset"></i>
                    <span>联系客服</span>
                </h4>
                <span class="contact-close" onclick="closeContactModal()">&times;</span>
            </div>
            <div class="contact-modal-body">
                <?php if (!empty($settings['contact_wechat'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_wechat']); ?>', '微信')">
                    <i class="fab fa-weixin"></i>
                    <span>微信：<?php echo htmlspecialchars($settings['contact_wechat']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_qq'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_qq']); ?>', 'QQ')">
                    <i class="fab fa-qq"></i>
                    <span>QQ：<?php echo htmlspecialchars($settings['contact_qq']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_email'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_email']); ?>', '邮箱')">
                    <i class="fas fa-envelope"></i>
                    <span>邮箱：<?php echo htmlspecialchars($settings['contact_email']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_phone'])): ?>
                <div class="contact-modal-item" onclick="copyContactInfo('<?php echo htmlspecialchars($settings['contact_phone']); ?>', '电话')">
                    <i class="fas fa-phone"></i>
                    <span>电话：<?php echo htmlspecialchars($settings['contact_phone']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['work_time'])): ?>
                <div class="contact-modal-item">
                    <i class="fas fa-clock"></i>
                    <span>工作时间：<?php echo htmlspecialchars($settings['work_time']); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($settings['contact_address'])): ?>
                <div class="contact-modal-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>地址：<?php echo htmlspecialchars($settings['contact_address']); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 页面底部信息 -->
    <footer class="site-footer">
        <div class="footer-content">
            <?php if (!empty($settings['copyright'])): ?>
            <div class="copyright">
                <?php echo htmlspecialchars($settings['copyright']); ?>
            </div>
            <?php else: ?>
            <div class="copyright">
                © 2024 红嘴鸥教育
            </div>
            <?php endif; ?>

            <?php if (!empty($settings['icp_number'])): ?>
            <div class="icp-info">
                <a href="https://beian.miit.gov.cn/" target="_blank" rel="noopener">
                    <?php echo htmlspecialchars($settings['icp_number']); ?>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </footer>

    <!-- 滚动提示 -->
    <div id="scrollHint" class="scroll-hint">
        <span id="scrollText">向下滑动，查看剩余内容</span>
    </div>

    <!-- 查询弹窗 -->
    <div id="queryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>兑换记录查询</h3>
                <span class="close" onclick="closeQueryModal()">&times;</span>
            </div>
            <div class="modal-body">
                <p>请输入要查询的卡密：</p>
                <input type="text" id="queryCardNumber" placeholder="请输入卡密" />
                <div class="modal-buttons">
                    <button onclick="queryExchangeRecord()" class="btn-primary">查询</button>
                    <button onclick="closeQueryModal()" class="btn-secondary">取消</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 将前端设置传递给JavaScript
        window.frontendSettings = <?php echo json_encode($frontendSettings ?? [], JSON_UNESCAPED_UNICODE); ?>;

        // 简化的Ajax函数
        function ajax(options) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(options.method || 'GET', options.url);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                xhr.onload = function() {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            resolve(JSON.parse(xhr.responseText));
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error('HTTP ' + xhr.status));
                    }
                };

                xhr.onerror = function() {
                    reject(new Error('网络错误'));
                };

                if (options.data) {
                    const formData = new URLSearchParams();
                    for (const key in options.data) {
                        formData.append(key, options.data[key]);
                    }
                    xhr.send(formData.toString());
                } else {
                    xhr.send();
                }
            });
        }

        // 加载通知弹窗
        function loadNotificationModal() {
            ajax({
                method: 'GET',
                url: '/admin/getNotificationConfig'
            }).then(function(response) {
                if (response.code === 1 && response.data.enabled) {
                    showNotificationModal(response.data);
                }
            }).catch(function(error) {
                // 获取通知配置失败，静默处理
            });
        }

        // 显示通知弹窗
        function showNotificationModal(config) {
            const modalHtml = createNotificationModal(config);

            // 将模态框添加到页面
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = document.getElementById('notificationModal');
            modal.style.display = 'block';

            // 设置自动关闭
            if (config.auto_close > 0) {
                setTimeout(function() {
                    closeNotificationModal();
                }, config.auto_close * 1000);
            }
        }

        // 创建通知弹窗HTML
        function createNotificationModal(config) {
            let modalHtml = '';

            switch(config.style) {
                case 'celebration':
                    modalHtml = `
                        <div class="modal fade show" id="notificationModal" style="display: none; background: rgba(0,0,0,0.5);">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content border-0 shadow-lg">
                                    <div class="modal-header bg-success bg-gradient text-white border-0">
                                        <h5 class="modal-title d-flex align-items-center">
                                            <i class="fas fa-trophy me-2"></i>
                                            <span class="me-2">🎉</span>
                                            ${config.title}
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" onclick="closeNotificationModal()"></button>
                                    </div>
                                    <div class="modal-body p-4 text-center">
                                        <div class="mb-3">
                                            <div class="bg-success bg-gradient rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 60px; height: 60px;">
                                                <i class="fas fa-star text-white fs-3"></i>
                                            </div>
                                        </div>
                                        <div class="notification-content">${config.content}</div>
                                    </div>
                                    <div class="modal-footer border-0 justify-content-center">
                                        <button type="button" class="btn btn-success" onclick="closeNotificationModal()">
                                            <i class="fas fa-check me-1"></i>知道了
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;

                case 'urgent':
                    modalHtml = `
                        <div class="modal fade show" id="notificationModal" style="display: none; background: rgba(0,0,0,0.5);">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content border-0 shadow-lg">
                                    <div class="modal-header bg-danger bg-gradient text-white border-0">
                                        <h5 class="modal-title d-flex align-items-center">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <span class="me-2">⚠️</span>
                                            ${config.title}
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" onclick="closeNotificationModal()"></button>
                                    </div>
                                    <div class="modal-body p-4">
                                        <div class="d-flex align-items-start">
                                            <div class="me-3">
                                                <div class="bg-danger bg-gradient rounded-circle d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="fas fa-bell text-white fs-5"></i>
                                                </div>
                                            </div>
                                            <div class="flex-grow-1">
                                                <div class="notification-content">${config.content}</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer border-0">
                                        <button type="button" class="btn btn-outline-danger" onclick="closeNotificationModal()">稍后处理</button>
                                        <button type="button" class="btn btn-danger" onclick="closeNotificationModal()">
                                            <i class="fas fa-check me-1"></i>立即处理
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;

                case 'daily':
                default:
                    modalHtml = `
                        <div class="modal fade show" id="notificationModal" style="display: none; background: rgba(0,0,0,0.5);">
                            <div class="modal-dialog modal-dialog-centered">
                                <div class="modal-content border-0 shadow">
                                    <div class="modal-header bg-primary bg-gradient text-white border-0">
                                        <h5 class="modal-title d-flex align-items-center">
                                            <i class="fas fa-info-circle me-2"></i>
                                            <span class="me-2">📢</span>
                                            ${config.title}
                                        </h5>
                                        <button type="button" class="btn-close btn-close-white" onclick="closeNotificationModal()"></button>
                                    </div>
                                    <div class="modal-body p-4">
                                        <div class="notification-content">${config.content}</div>
                                    </div>
                                    <div class="modal-footer border-0 justify-content-end">
                                        <button type="button" class="btn btn-primary" onclick="closeNotificationModal()">
                                            <i class="fas fa-check me-1"></i>确定
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    break;
            }

            return modalHtml;
        }

        // 关闭通知弹窗
        function closeNotificationModal() {
            const modal = document.getElementById('notificationModal');
            if (modal) {
                modal.style.display = 'none';
                modal.remove();
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 加载通知弹窗
            loadNotificationModal();

            // 滚动提示功能
            const container = document.querySelector('.container');
            const scrollHint = document.getElementById('scrollHint');
            const scrollText = document.getElementById('scrollText');

            // 动态文字数组
            const scrollTexts = [
                '向下滑动，查看剩余内容',
                '还有更多内容等你发现',
                '继续滚动查看详情',
                '下方有更多精彩内容'
            ];
            let textIndex = 0;
            let textInterval;

            function startTextAnimation() {
                textInterval = setInterval(() => {
                    textIndex = (textIndex + 1) % scrollTexts.length;
                    scrollText.style.opacity = '0';
                    setTimeout(() => {
                        scrollText.textContent = scrollTexts[textIndex];
                        scrollText.style.opacity = '1';
                    }, 200);
                }, 3000);
            }

            function stopTextAnimation() {
                if (textInterval) {
                    clearInterval(textInterval);
                    textInterval = null;
                }
            }

            function checkScroll() {
                if (container.scrollHeight > container.clientHeight) {
                    const scrollTop = container.scrollTop;
                    const scrollHeight = container.scrollHeight;
                    const clientHeight = container.clientHeight;
                    const scrollPercentage = scrollTop / (scrollHeight - clientHeight);

                    // 当内容可滚动且未滚动到底部时显示提示
                    if (scrollPercentage < 0.9) {
                        scrollHint.classList.add('show');
                        if (!textInterval) {
                            startTextAnimation();
                        }
                    } else {
                        scrollHint.classList.remove('show');
                        stopTextAnimation();
                    }
                } else {
                    scrollHint.classList.remove('show');
                    stopTextAnimation();
                }
            }

            // 监听滚动事件
            container.addEventListener('scroll', checkScroll);

            // 监听内容变化
            const observer = new MutationObserver(function() {
                setTimeout(checkScroll, 100);
            });

            observer.observe(container, {
                childList: true,
                subtree: true,
                attributes: true
            });

            // 初始检查
            setTimeout(checkScroll, 500);

            // 添加文字过渡效果
            scrollText.style.transition = 'opacity 0.3s ease';
        });
    </script>
    <script src="/static/js/alert-modal.js"></script>
    <script src="/static/js/query-modal.js"></script>
    <script src="/static/js/contact-modal.js"></script>
    <script src="/static/js/exchange.js"></script>
</body>
</html>
