<?php

namespace app\model;

use think\Model;

class Card extends Model
{
    protected $table = 'cards';
    
    // 设置字段信息
    protected $schema = [
        'id'           => 'int',
        'card_number'  => 'string',
        'card_type'    => 'string',
        'batch_id'     => 'string',
        'category_id'  => 'int',
        'content_ids'  => 'string',
        'value'        => 'float',
        'valid_days'   => 'int',
        'max_use_count' => 'int',
        'used_count'   => 'int',
        'status'       => 'int',
        'used_time'    => 'datetime',
        'used_ip'      => 'string',
        'expire_time'  => 'datetime',
        'remark'       => 'string',
        'create_time'  => 'datetime',
        'update_time'  => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // JSON字段
    protected $json = ['content_ids'];
    
    // 状态常量
    const STATUS_DISABLED = 0;  // 已禁用
    const STATUS_UNUSED = 1;    // 未使用
    const STATUS_USED = 2;      // 已使用
    
    /**
     * 根据卡密号码查找
     */
    public static function findByCardNumber($cardNumber)
    {
        return self::where('card_number', $cardNumber)->find();
    }
    
    /**
     * 检查卡密是否可用
     */
    public function isAvailable()
    {
        // 检查状态
        if ($this->status === self::STATUS_DISABLED) {
            return false;
        }

        // 检查使用次数限制
        if ($this->used_count >= $this->max_use_count) {
            return false;
        }

        // 检查是否过期
        if ($this->expire_time && strtotime($this->expire_time) < time()) {
            return false;
        }

        return true;
    }
    
    /**
     * 标记为已使用（增加使用次数）
     */
    public function markAsUsed($ip = null)
    {
        $this->used_count += 1;

        // 如果是第一次使用，设置首次使用时间
        if ($this->used_count == 1) {
            $this->used_time = date('Y-m-d H:i:s');
            $this->used_ip = $ip ?: request()->ip();
        }

        // 如果达到最大使用次数，标记为已使用
        if ($this->used_count >= $this->max_use_count) {
            $this->status = self::STATUS_USED;
        }

        return $this->save();
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_DISABLED => '已禁用',
            self::STATUS_UNUSED => '未使用',
            self::STATUS_USED => '已使用',
        ];

        $statusText = $statusMap[$data['status']] ?? '未知';

        // 如果卡密支持多次使用，显示使用次数信息
        if (isset($data['max_use_count']) && $data['max_use_count'] > 1) {
            $usedCount = $data['used_count'] ?? 0;
            $maxCount = $data['max_use_count'];
            $statusText .= " ({$usedCount}/{$maxCount})";
        }

        return $statusText;
    }
    
    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }
}
