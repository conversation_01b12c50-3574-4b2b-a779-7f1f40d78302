<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db;

class InitDatabase extends Command
{
    protected function configure()
    {
        $this->setName('init:database')
             ->setDescription('初始化数据库和测试数据');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            $output->writeln('开始初始化数据库...');
            
            // 读取并执行SQL文件
            $sqlFile = root_path() . 'database/init.sql';
            if (!file_exists($sqlFile)) {
                $output->writeln('<error>SQL文件不存在: ' . $sqlFile . '</error>');
                return;
            }
            
            $sql = file_get_contents($sqlFile);
            $statements = explode(';', $sql);
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    try {
                        Db::execute($statement);
                    } catch (\Exception $e) {
                        // 忽略表已存在等错误
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            $output->writeln('<comment>警告: ' . $e->getMessage() . '</comment>');
                        }
                    }
                }
            }
            
            $output->writeln('<info>数据库初始化完成！</info>');
            $output->writeln('');
            $output->writeln('测试账号信息：');
            $output->writeln('管理员账号: admin');
            $output->writeln('管理员密码: 123456');
            $output->writeln('');
            $output->writeln('测试卡密：');
            $output->writeln('XXXX-XXXX-XXXX-1234 (网络安全)');
            $output->writeln('XXXX-XXXX-XXXX-5678 (月度会员)');
            $output->writeln('XXXX-XXXX-XXXX-9012 (编程开发)');
            
        } catch (\Exception $e) {
            $output->writeln('<error>初始化失败: ' . $e->getMessage() . '</error>');
        }
    }
}
