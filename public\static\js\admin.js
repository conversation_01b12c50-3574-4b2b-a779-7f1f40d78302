/**
 * 管理后台JavaScript - 现代化交互
 */

// 全局配置
const AdminConfig = {
    apiBase: '/admin',
    timeout: 10000,
    pageSize: 20
};

// 工具函数
const AdminUtils = {
    /**
     * 显示消息提示
     */
    showMessage: function(message, type = 'info', duration = 3000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        };
        
        // 创建消息容器
        let messageContainer = document.getElementById('message-container');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.id = 'message-container';
            messageContainer.className = 'position-fixed top-0 end-0 p-3';
            messageContainer.style.zIndex = '9999';
            document.body.appendChild(messageContainer);
        }
        
        // 创建消息元素
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div class="alert ${alertClass[type]} alert-dismissible fade show" role="alert" id="${alertId}">
                <i class="fas fa-${this.getIconByType(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        messageContainer.insertAdjacentHTML('beforeend', alertHtml);
        
        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    alert.remove();
                }
            }, duration);
        }
    },
    
    /**
     * 根据类型获取图标
     */
    getIconByType: function(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-circle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    /**
     * AJAX请求封装
     */
    ajax: function(options) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: AdminConfig.timeout
        };
        
        const config = Object.assign({}, defaults, options);
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            xhr.timeout = config.timeout;
            xhr.ontimeout = () => reject(new Error('请求超时'));
            xhr.onerror = () => reject(new Error('网络错误'));
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            resolve(response);
                        } catch (e) {
                            resolve(xhr.responseText);
                        }
                    } else {
                        reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                    }
                }
            };
            
            xhr.open(config.method, config.url);
            
            // 设置请求头
            for (const header in config.headers) {
                xhr.setRequestHeader(header, config.headers[header]);
            }
            
            // 发送数据
            if (config.data) {
                if (config.headers['Content-Type'] === 'application/json') {
                    xhr.send(JSON.stringify(config.data));
                } else {
                    xhr.send(config.data);
                }
            } else {
                xhr.send();
            }
        });
    },
    
    /**
     * 格式化日期
     */
    formatDate: function(date, format = 'YYYY-MM-DD HH:mm:ss') {
        if (!date) return '';
        
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 确认对话框
     */
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
};

// 侧边栏管理
const SidebarManager = {
    init: function() {
        this.bindEvents();
        this.handleResize();
    },
    
    bindEvents: function() {
        // 侧边栏切换按钮
        const toggleButtons = document.querySelectorAll('.sidebar-toggle');
        toggleButtons.forEach(button => {
            button.addEventListener('click', this.toggle.bind(this));
        });
        
        // 窗口大小改变
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // 点击外部关闭侧边栏（移动端）
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    },
    
    toggle: function() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.toggle('show');

        // 移动端添加/移除遮罩层
        if (window.innerWidth <= 768) {
            this.toggleOverlay();
        }
    },

    toggleOverlay: function() {
        let overlay = document.querySelector('.sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.className = 'sidebar-overlay';
            document.body.appendChild(overlay);

            // 点击遮罩层关闭侧边栏
            overlay.addEventListener('click', () => {
                this.closeSidebar();
            });
        }

        const sidebar = document.getElementById('sidebar');
        if (sidebar.classList.contains('show')) {
            overlay.classList.add('show');
            document.body.style.overflow = 'hidden';
        } else {
            overlay.classList.remove('show');
            document.body.style.overflow = '';
        }
    },

    closeSidebar: function() {
        const sidebar = document.getElementById('sidebar');
        sidebar.classList.remove('show');

        const overlay = document.querySelector('.sidebar-overlay');
        if (overlay) {
            overlay.classList.remove('show');
        }
        document.body.style.overflow = '';
    },
    
    handleResize: function() {
        if (window.innerWidth > 768) {
            this.closeSidebar();
        }
    },
    
    handleOutsideClick: function(e) {
        if (window.innerWidth <= 768) {
            const sidebar = document.getElementById('sidebar');
            const toggleButtons = document.querySelectorAll('.sidebar-toggle');

            let isToggleButton = false;
            toggleButtons.forEach(button => {
                if (button.contains(e.target)) {
                    isToggleButton = true;
                }
            });

            if (!sidebar.contains(e.target) && !isToggleButton && sidebar.classList.contains('show')) {
                this.closeSidebar();
            }
        }
    }
};

// 表格管理
const TableManager = {
    /**
     * 初始化表格
     */
    init: function(tableId, options = {}) {
        const table = document.getElementById(tableId);
        if (!table) return;
        
        this.addHoverEffects(table);
        this.addSortingIfNeeded(table, options);
    },
    
    /**
     * 添加悬停效果
     */
    addHoverEffects: function(table) {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    },
    
    /**
     * 添加排序功能
     */
    addSortingIfNeeded: function(table, options) {
        if (!options.sortable) return;
        
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.innerHTML += ' <i class="fas fa-sort ms-1"></i>';
            
            header.addEventListener('click', () => {
                this.sortTable(table, header.dataset.sort);
            });
        });
    },
    
    /**
     * 表格排序
     */
    sortTable: function(table, column) {
        // 简单的表格排序实现
        console.log('Sorting table by column:', column);
    }
};

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化侧边栏
    SidebarManager.init();
    
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // 自动隐藏成功消息
    setTimeout(() => {
        const successAlerts = document.querySelectorAll('.alert-success');
        successAlerts.forEach(alert => {
            if (alert.classList.contains('auto-hide')) {
                alert.style.transition = 'opacity 0.5s';
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 500);
            }
        });
    }, 3000);
});

// 导出到全局
window.AdminUtils = AdminUtils;
window.SidebarManager = SidebarManager;
window.TableManager = TableManager;
