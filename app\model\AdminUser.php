<?php

namespace app\model;

use think\Model;

class AdminUser extends Model
{
    protected $table = 'admin_users';
    
    // 设置字段信息
    protected $schema = [
        'id'              => 'int',
        'username'        => 'string',
        'password'        => 'string',
        'nickname'        => 'string',
        'email'           => 'string',
        'phone'           => 'string',
        'avatar'          => 'string',
        'role'            => 'string',
        'status'          => 'int',
        'last_login_time' => 'datetime',
        'last_login_ip'   => 'string',
        'create_time'     => 'datetime',
        'update_time'     => 'datetime',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = true;
    
    // 隐藏字段
    protected $hidden = ['password'];
    
    // 状态常量
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;
    
    /**
     * 根据用户名查找
     */
    public static function findByUsername($username)
    {
        return self::where('username', $username)
                   ->where('status', self::STATUS_ENABLED)
                   ->find();
    }
    
    /**
     * 验证密码
     */
    public function checkPassword($password)
    {
        return password_verify($password, $this->password);
    }
    
    /**
     * 更新登录信息
     */
    public function updateLoginInfo()
    {
        $this->last_login_time = date('Y-m-d H:i:s');
        $this->last_login_ip = request()->ip();
        return $this->save();
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_DISABLED => '禁用',
            self::STATUS_ENABLED => '启用',
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }
}
