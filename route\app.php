<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2018 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------
use think\facade\Route;

// 用户前端路由
Route::get('/', 'index/index');
Route::post('/exchange', 'index/exchange');
Route::post('/query', 'index/query');

// 下载路由
Route::get('/download/:id', 'download/index');

// 管理后端路由
Route::group('admin', function () {
    Route::get('login', 'admin/login');
    Route::post('login', 'admin/login');
    Route::get('logout', 'admin/logout');
    Route::get('dashboard', 'admin/dashboard');
    Route::get('getStats', 'admin/getStats');
    Route::get('cards', 'admin/cards');
    Route::get('exportCards', 'admin/exportCards');
    Route::get('getCardDetails', 'admin/getCardDetails');
    Route::post('getCardDetail', 'admin/getCardDetail');
    Route::post('generateCards', 'admin/generateCards');
    Route::post('deleteCard', 'admin/deleteCard');
    Route::post('batchDeleteCards', 'admin/batchDeleteCards');
    Route::post('toggleCardStatus', 'admin/toggleCardStatus');
    Route::get('categories', 'admin/categories');
    Route::get('getCategory', 'admin/getCategory');
    Route::get('getCategoryTree', 'admin/getCategoryTree');
    Route::get('getCategoriesByLevel', 'admin/getCategoriesByLevel');
    Route::get('getCategoryContents', 'admin/getCategoryContents');
    Route::get('getCategoryContent', 'admin/getCategoryContent');
    Route::post('saveCategory', 'admin/saveCategory');
    Route::post('deleteCategory', 'admin/deleteCategory');
    Route::post('batchDeleteCategories', 'admin/batchDeleteCategories');
    Route::post('updateCategoryStatus', 'admin/updateCategoryStatus');
    Route::post('updateCategorySort', 'admin/updateCategorySort');
    Route::get('contents', 'admin/contents');
    Route::get('getContentList', 'admin/getContentList');
    Route::get('getContent', 'admin/getContent');
    Route::get('getCategoryPath', 'admin/getCategoryPathApi');
    Route::post('saveContent', 'admin/saveContent');
    Route::post('toggleContentStatus', 'admin/toggleContentStatus');
    Route::post('updateContentSort', 'admin/updateContentSort');
    Route::post('deleteContent', 'admin/deleteContent');
    Route::get('settings', 'admin/settings');
    Route::get('card-settings', 'admin/cardSettings');
    Route::get('getCardSettings', 'admin/getCardSettings');
    Route::post('saveCardSettings', 'admin/saveCardSettings');
    Route::get('frontend-settings', 'admin/frontendSettings');
    Route::post('saveFrontendSettings', 'admin/saveFrontendSettings');
    Route::get('profile', 'admin/profile');
    Route::post('updateProfile', 'admin/updateProfile');
    Route::post('changePassword', 'admin/changePassword');
});

// 兼容旧路由
Route::get('think', function () {
    return 'hello,ThinkPHP8!';
});

Route::get('hello/:name', 'index/hello');
