---
type: "always_apply"
---

1、这是一个ThinkPHP 8.1.2项目，前端使用的是Bootstrap，开发请遵循ThinkPHP 8.1.2和Bootstrap的规范。
2、请尽量不要创建临时的测试的文件，如果必须要创建，请在完成任务后删除。
3、修改新增文件、功能或数据库，请先检查原来的是否存在，如果存在请在原来的上面修改，如果不存在，请按照规范创建新的。
4、完成任务后，不需要你创建总结文档。
5、要保持整个系统风格统一，优先使用全局样式来修改，如果全局样式解决不了在考虑自定义样式。
6、优先使用现有组件，首先检查已有的CSS组件库
7、复用胜过重写，能复用的绝不重新创建
8、外链胜过内联，所有样式和脚本都使用外部文件
9、组件化思维，新增样式要考虑复用性
10、开发过程中如果使用了调试代码，请在完成任务后删除掉调试代码！
11、请你在每次完成任务后，都应该检查你写的代码是否存在安全漏洞，如果存在，请及时解决。
12、此项目根目录F:\linshi\thphp\kmxt
13、开发过程中如果需要数据库的信息，请参考 @数据表.md 文档，如果已经有了，请直接使用，如果需要新创建，请在 @数据表.md 文档中更新记录！