<?php

namespace app\model;

use think\Model;

class AccountLockout extends Model
{
    protected $table = 'account_lockouts';
    
    // 设置字段信息
    protected $schema = [
        'id'             => 'int',
        'username'       => 'string',
        'ip_address'     => 'string',
        'locked_at'      => 'datetime',
        'unlock_at'      => 'datetime',
        'failure_count'  => 'int',
        'status'         => 'int',
        'unlock_reason'  => 'string',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'locked_at';
    
    // 状态常量
    const STATUS_UNLOCKED = 0;  // 已解锁
    const STATUS_LOCKED = 1;    // 锁定中
    
    /**
     * 锁定账户
     */
    public static function lockAccount($username, $failureCount, $lockoutMinutes)
    {
        $unlockTime = date('Y-m-d H:i:s', time() + ($lockoutMinutes * 60));
        
        return self::create([
            'username' => $username,
            'ip_address' => request()->ip(),
            'unlock_at' => $unlockTime,
            'failure_count' => $failureCount,
            'status' => self::STATUS_LOCKED,
        ]);
    }
    
    /**
     * 检查账户是否被锁定
     */
    public static function isAccountLocked($username)
    {
        $lockout = self::where('username', $username)
                       ->where('status', self::STATUS_LOCKED)
                       ->where('unlock_at', '>', date('Y-m-d H:i:s'))
                       ->order('locked_at', 'desc')
                       ->find();
        
        return $lockout ? $lockout : false;
    }
    
    /**
     * 解锁过期的账户
     */
    public static function unlockExpiredAccounts()
    {
        return self::where('status', self::STATUS_LOCKED)
                   ->where('unlock_at', '<=', date('Y-m-d H:i:s'))
                   ->update([
                       'status' => self::STATUS_UNLOCKED,
                       'unlock_reason' => '自动解锁（时间到期）'
                   ]);
    }
    
    /**
     * 手动解锁账户
     */
    public static function unlockAccount($username, $reason = '管理员手动解锁')
    {
        return self::where('username', $username)
                   ->where('status', self::STATUS_LOCKED)
                   ->update([
                       'status' => self::STATUS_UNLOCKED,
                       'unlock_reason' => $reason
                   ]);
    }
    
    /**
     * 获取锁定剩余时间（分钟）
     */
    public function getRemainingMinutes()
    {
        if ($this->status != self::STATUS_LOCKED) {
            return 0;
        }
        
        $remaining = strtotime($this->unlock_at) - time();
        return $remaining > 0 ? ceil($remaining / 60) : 0;
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_UNLOCKED => '已解锁',
            self::STATUS_LOCKED => '锁定中',
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }
}
