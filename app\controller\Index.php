<?php

namespace app\controller;

use app\BaseController;
use app\model\Card;
use app\model\Content;
use app\model\ExchangeRecord;
use think\facade\Db;
use think\facade\View;

class Index extends BaseController
{
    /**
     * 首页 - 卡密兑换页面
     */
    public function index()
    {
        try {
            // 获取网站设置
            $settings = $this->getSiteSettings();

            // 检查网站状态
            if (isset($settings['site_status']) && $settings['site_status'] === '0') {
                // 网站维护中，显示维护页面
                View::assign('settings', $settings);
                return View::fetch('index/maintenance');
            }

            // 获取前端设置（提示消息等）
            $frontendSettings = $this->getFrontendSettings();

            // 传递设置数据到视图
            View::assign('settings', $settings);
            View::assign('frontendSettings', $frontendSettings);

            return View::fetch('index/simple');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage() . '<br>File: ' . $e->getFile() . '<br>Line: ' . $e->getLine();
        }
    }

    /**
     * 获取网站设置
     */
    private function getSiteSettings()
    {
        try {
            $db = Db::connect();
            $settings = $db->table('settings')->column('value', 'key');

            // 获取前端设置
            $frontendConfigFile = app()->getRootPath() . 'config/frontend_settings.php';
            $frontendSettings = [];
            if (file_exists($frontendConfigFile)) {
                $frontendSettings = include $frontendConfigFile;
            }

            // 设置默认值
            $defaults = [
                'site_name' => '红嘴鸥教育',
                'site_title' => '红嘴鸥教育',
                'site_description' => '电子资料兑换系统',
                'site_subtitle' => '',
                'site_logo' => '',
                'site_status' => '1',
                'icp_number' => '',
                'copyright' => '© 2024 红嘴鸥教育',
                'page_footer_notice' => '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888'
            ];

            // 合并系统设置和前端设置，系统设置优先级更高
            $allSettings = array_merge($defaults, $frontendSettings, $settings);

            return $allSettings;
        } catch (\Exception $e) {
            // 如果获取失败，返回默认设置
            return [
                'site_name' => '红嘴鸥教育',
                'site_title' => '红嘴鸥教育',
                'site_description' => '电子资料兑换系统',
                'site_subtitle' => '',
                'site_logo' => '',
                'icp_number' => '',
                'copyright' => '© 2024 红嘴鸥教育',
                'page_footer_notice' => '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888'
            ];
        }
    }

    /**
     * 获取前端设置
     */
    private function getFrontendSettings()
    {
        try {
            $frontendConfigFile = app()->getRootPath() . 'config/frontend_settings.php';
            $frontendSettings = [];
            if (file_exists($frontendConfigFile)) {
                $frontendSettings = include $frontendConfigFile;
            }

            // 设置默认值
            $defaults = [
                // 兑换确认对话框
                'exchange_confirm_title' => '兑换确认',
                'exchange_confirm_content' => '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！',
                // 验证提示
                'empty_card_message' => '请输入卡密',
                // 兑换错误提示
                'card_not_exist_message' => '卡密不存在或已失效',
                'card_already_used_message' => '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！',
                'card_disabled_message' => '卡密已被禁用，请联系微信hzoedu888',
                'card_expired_message' => '卡密已过期',
                // 查询错误提示
                'query_disabled_message' => '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888',
                'card_not_exchanged_message' => '该卡密尚未兑换，请先兑换后再查看兑换记录',
                'no_exchange_record_message' => '未找到兑换记录',
                // 成功提示
                'exchange_success_message' => '兑换成功！',
                'query_success_message' => '查询成功',
                // 复制功能提示
                'copy_success_message' => '✅ 链接复制成功！',
                'copy_error_message' => '❌ 复制失败，请手动复制',
                // 网络错误提示
                'network_error_message' => '网络错误，请稍后重试',
            ];

            return array_merge($defaults, $frontendSettings);
        } catch (\Exception $e) {
            // 如果获取失败，返回默认设置
            return [
                'exchange_confirm_title' => '兑换确认',
                'exchange_confirm_content' => '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！',
                'empty_card_message' => '请输入卡密',
                'card_not_exist_message' => '卡密不存在或已失效',
                'card_already_used_message' => '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！',
                'card_disabled_message' => '卡密已被禁用，请联系微信hzoedu888',
                'card_expired_message' => '卡密已过期',
                'query_disabled_message' => '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888',
                'card_not_exchanged_message' => '该卡密尚未兑换，请先兑换后再查看兑换记录',
                'no_exchange_record_message' => '未找到兑换记录',
                'exchange_success_message' => '兑换成功！',
                'query_success_message' => '查询成功',
                'copy_success_message' => '✅ 链接复制成功！',
                'copy_error_message' => '❌ 复制失败，请手动复制',
                'network_error_message' => '网络错误，请稍后重试',
            ];
        }
    }

    /**
     * 卡密兑换处理
     */
    public function exchange()
    {
        // 清理输出缓冲区，确保纯净的JSON响应
        while (ob_get_level()) {
            ob_end_clean();
        }

        try {
            // 获取前端设置
            $frontendSettings = $this->getFrontendSettings();

            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $cardNumber = trim($this->request->post('card_number', ''));

            // 验证卡密格式
            if (empty($cardNumber)) {
                return json(['code' => 0, 'msg' => $frontendSettings['empty_card_message']]);
            }

            // 查找卡密
            $card = Card::where('card_number', $cardNumber)->find();
            if (!$card) {
                return json(['code' => 0, 'msg' => $frontendSettings['card_not_exist_message']]);
            }

            // 检查卡密是否可用
            if ($card->status == Card::STATUS_DISABLED) {
                return json(['code' => 0, 'msg' => $frontendSettings['card_disabled_message']]);
            }

            // 检查使用次数限制
            if ($card->used_count >= $card->max_use_count) {
                return json(['code' => 0, 'msg' => $frontendSettings['card_already_used_message']]);
            }

            // 检查是否过期
            if ($card->expire_time && strtotime($card->expire_time) < time()) {
                return json(['code' => 0, 'msg' => $frontendSettings['card_expired_message']]);
            }

            // 增加使用次数
            $card->used_count += 1;

            // 如果是第一次使用，设置首次使用时间
            if ($card->used_count == 1) {
                $card->used_time = date('Y-m-d H:i:s');
                $card->used_ip = $this->request->ip();
            }

            // 如果达到最大使用次数，标记为已使用
            if ($card->used_count >= $card->max_use_count) {
                $card->status = Card::STATUS_USED;
            }

            $card->save();

            // 获取关联的内容详情
            $contents = [];
            if ($card->content_ids) {
                $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);

                if ($contentIds && is_array($contentIds)) {
                    $contentList = Content::whereIn('id', $contentIds)
                                         ->where('status', 1) // 只获取已发布的内容
                                         ->with('category')
                                         ->select();

                    foreach ($contentList as $content) {
                        $contents[] = [
                            'id' => $content->id,
                            'title' => $content->title,
                            'description' => $content->description,
                            'content' => $content->content, // 添加详细内容
                            'category_name' => $content->category ? $content->category->name : '未分类',
                            'file_size' => $content->file_size,
                            'file_size_format' => $this->formatFileSize($content->file_size),
                            'download_url' => $content->download_url ?: '/download/' . $content->id,
                            'create_time' => $content->create_time
                        ];
                    }
                }
            }

            // 创建兑换记录
            ExchangeRecord::create([
                'card_id' => $card->id,
                'card_number' => $card->card_number,
                'content_ids' => json_encode($card->content_ids ?: []),
                'exchange_ip' => $this->request->ip(),
                'user_agent' => $this->request->header('user-agent'),
                'status' => ExchangeRecord::STATUS_SUCCESS,
                'remark' => '兑换成功'
            ]);

            // 获取推广模块配置
            $promotionConfig = $this->getPromotionConfig();

            return json([
                'code' => 1,
                'msg' => $frontendSettings['exchange_success_message'],
                'data' => [
                    'exchange_time' => date('Y-m-d H:i:s'),
                    'contents' => $contents,
                    'promotion' => $promotionConfig
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '系统错误：' . $e->getMessage()]);
        }
    }

    /**
     * 格式化文件大小
     */
    private function formatFileSize($bytes)
    {
        if ($bytes == 0) return '0 B';

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $i = floor(log($bytes, 1024));

        return round($bytes / pow(1024, $i), 2) . ' ' . $units[$i];
    }

    /**
     * 记录无效尝试
     */
    private function logInvalidAttempt($cardNumber, $reason)
    {
        try {
            ExchangeRecord::create([
                'card_id' => 0,
                'card_number' => $cardNumber,
                'content_ids' => json_encode([]),
                'exchange_ip' => $this->request->ip(),
                'user_agent' => $this->request->header('user-agent'),
                'status' => ExchangeRecord::STATUS_FAILED,
                'remark' => '无效尝试：' . $reason
            ]);
        } catch (\Exception $e) {
            // 记录失败不影响主流程
        }
    }

    /**
     * 查询兑换记录
     */
    public function query()
    {
        // 清理输出缓冲区，确保纯净的JSON响应
        while (ob_get_level()) {
            ob_end_clean();
        }

        try {
            // 获取前端设置
            $frontendSettings = $this->getFrontendSettings();

            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $cardNumber = trim($this->request->post('card_number', ''));

            if (empty($cardNumber)) {
                return json(['code' => 0, 'msg' => $frontendSettings['empty_card_message']]);
            }

            // 首先检查卡密是否存在
            $card = Card::where('card_number', $cardNumber)->find();
            if (!$card) {
                return json(['code' => 0, 'msg' => $frontendSettings['card_not_exist_message']]);
            }

            // 检查卡密状态
            if ($card->status == Card::STATUS_DISABLED) {
                // 禁用状态：检查是否曾经兑换过
                $hasExchangeRecord = ExchangeRecord::where('card_number', $cardNumber)
                                                  ->where('status', ExchangeRecord::STATUS_SUCCESS)
                                                  ->count() > 0;

                if ($hasExchangeRecord) {
                    // 已兑换但被禁用
                    return json(['code' => 0, 'msg' => $frontendSettings['query_disabled_message']]);
                } else {
                    // 未兑换且被禁用
                    return json(['code' => 0, 'msg' => $frontendSettings['card_not_exchanged_message']]);
                }
            } elseif ($card->used_count == 0) {
                // 未兑换状态（使用次数为0）
                return json(['code' => 0, 'msg' => $frontendSettings['card_not_exchanged_message']]);
            }

            // 查询兑换记录
            $records = ExchangeRecord::where('card_number', $cardNumber)
                                    ->where('status', ExchangeRecord::STATUS_SUCCESS)
                                    ->order('exchange_time', 'desc')
                                    ->limit(5)
                                    ->select();

            if ($records->isEmpty()) {
                return json(['code' => 0, 'msg' => $frontendSettings['no_exchange_record_message']]);
            }

            // 获取关联的内容详情
            $contents = [];
            if ($card->content_ids) {
                $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);
                if ($contentIds && is_array($contentIds)) {
                    $contentList = Content::whereIn('id', $contentIds)
                                         ->where('status', 1)
                                         ->with('category')
                                         ->select();

                    foreach ($contentList as $content) {
                        $contents[] = [
                            'id' => $content->id,
                            'title' => $content->title,
                            'description' => $content->description,
                            'content' => $content->content, // 添加详细内容
                            'category_name' => $content->category ? $content->category->name : '未分类',
                            'file_size' => $content->file_size,
                            'file_size_format' => $this->formatFileSize($content->file_size),
                            'download_url' => $content->download_url ?: '/download/' . $content->id,
                            'create_time' => $content->create_time
                        ];
                    }
                }
            }

            // 准备卡密信息
            $cardInfo = [
                'card_type' => $card->card_type ?: '通用卡密',
                'value' => $card->value ?: '0.00',
                'status' => $card->status == Card::STATUS_USED ? '已使用' : '未使用',
                'expire_time' => $card->expire_time ?: '永久有效',
                'used_time' => $card->used_time
            ];

            // 准备兑换记录
            $recordData = [];
            foreach ($records as $record) {
                $recordData[] = [
                    'exchange_time' => $record->exchange_time,
                    'status' => $record->status == ExchangeRecord::STATUS_SUCCESS ? '成功' : '失败',
                    'remark' => $record->remark
                ];
            }

            // 获取推广模块配置
            $promotionConfig = $this->getPromotionConfig();

            return json([
                'code' => 1,
                'msg' => $frontendSettings['query_success_message'],
                'data' => [
                    'card_info' => $cardInfo,
                    'contents' => $contents,
                    'records' => $recordData,
                    'promotion' => $promotionConfig
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '查询失败：' . $e->getMessage()]);
        }
    }



    /**
     * 获取推广模块配置
     */
    private function getPromotionConfig()
    {
        try {
            // 从前端设置配置文件获取推广配置
            $frontendConfigFile = app()->getRootPath() . 'config/frontend_settings.php';
            $frontendSettings = [];
            if (file_exists($frontendConfigFile)) {
                $frontendSettings = include $frontendConfigFile;
            }

            // 优先使用前端设置文件的配置
            if (!empty($frontendSettings) && isset($frontendSettings['promotion_enabled'])) {
                // 使用前端设置文件的数据，如果字段为空则使用默认值
                $promotionConfig = [
                    'enabled' => $frontendSettings['promotion_enabled'] ?? '0',
                    'title' => !empty($frontendSettings['promotion_title']) ? $frontendSettings['promotion_title'] : '您还可以点击以下按钮获取更多免费资源',
                    'buttons' => [
                        [
                            'text' => !empty($frontendSettings['promotion_btn1_text']) ? $frontendSettings['promotion_btn1_text'] : '电子资料包',
                            'url' => !empty($frontendSettings['promotion_btn1_url']) ? $frontendSettings['promotion_btn1_url'] : 'https://example.com/resources',
                            'color' => '#6f42c1'
                        ],
                        [
                            'text' => !empty($frontendSettings['promotion_btn2_text']) ? $frontendSettings['promotion_btn2_text'] : '免费网课',
                            'url' => !empty($frontendSettings['promotion_btn2_url']) ? $frontendSettings['promotion_btn2_url'] : 'https://example.com/courses',
                            'color' => '#6f42c1'
                        ],
                        [
                            'text' => !empty($frontendSettings['promotion_btn3_text']) ? $frontendSettings['promotion_btn3_text'] : '官方网站',
                            'url' => !empty($frontendSettings['promotion_btn3_url']) ? $frontendSettings['promotion_btn3_url'] : 'https://example.com',
                            'color' => '#6f42c1'
                        ]
                    ],
                    'contact' => [
                        'text' => !empty($frontendSettings['promotion_contact_text']) ? $frontendSettings['promotion_contact_text'] : '唯一售后微信：',
                        'value' => !empty($frontendSettings['promotion_contact_value']) ? $frontendSettings['promotion_contact_value'] : 'hzoedu888'
                    ]
                ];
            } else {
                // 如果前端设置文件中没有推广配置，则从settings表获取
                $settings = Db::table('settings')
                             ->whereIn('key', [
                                 'promotion_title',
                                 'promotion_btn1_text',
                                 'promotion_btn1_url',
                                 'promotion_btn2_text',
                                 'promotion_btn2_url',
                                 'promotion_btn3_text',
                                 'promotion_btn3_url',
                                 'promotion_contact_text',
                                 'promotion_contact_value',
                                 'promotion_enabled'
                             ])
                             ->column('value', 'key');

                // 使用settings表的数据
                $promotionConfig = [
                    'enabled' => $settings['promotion_enabled'] ?? '1',
                    'title' => $settings['promotion_title'] ?? '您还可以点击以下按钮获取更多免费资源',
                    'buttons' => [
                        [
                            'text' => $settings['promotion_btn1_text'] ?? '电子资料包',
                            'url' => $settings['promotion_btn1_url'] ?? '#',
                            'color' => '#6f42c1'
                        ],
                        [
                            'text' => $settings['promotion_btn2_text'] ?? '免费网课',
                            'url' => $settings['promotion_btn2_url'] ?? '#',
                            'color' => '#6f42c1'
                        ],
                        [
                            'text' => $settings['promotion_btn3_text'] ?? '官方网站',
                            'url' => $settings['promotion_btn3_url'] ?? '#',
                            'color' => '#6f42c1'
                        ]
                    ],
                    'contact' => [
                        'text' => $settings['promotion_contact_text'] ?? '唯一售后微信：',
                        'value' => $settings['promotion_contact_value'] ?? 'hzoedu888'
                    ]
                ];
            }

            return $promotionConfig;

        } catch (\Exception $e) {
            // 返回默认配置
            return [
                'enabled' => '1',
                'title' => '您还可以点击以下按钮获取更多免费资源',
                'buttons' => [
                    [
                        'text' => '电子资料包',
                        'url' => '#',
                        'color' => '#6f42c1'
                    ],
                    [
                        'text' => '免费网课',
                        'url' => '#',
                        'color' => '#6f42c1'
                    ],
                    [
                        'text' => '官方网站',
                        'url' => '#',
                        'color' => '#6f42c1'
                    ]
                ],
                'contact' => [
                    'text' => '唯一售后微信：',
                    'value' => 'hzoedu888'
                ]
            ];
        }
    }

    public function hello($name = 'ThinkPHP8')
    {
        return 'hello,' . $name;
    }
}
