<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站维护中 - <?php echo htmlspecialchars($settings['site_name'] ?? '卡密兑换系统'); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: -apple-system, BlinkMacSystemFont, "Se<PERSON>e UI", Robot<PERSON>, sans-serif;
        }
        
        .maintenance-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .maintenance-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        .maintenance-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 1rem;
        }
        
        .maintenance-subtitle {
            font-size: 1.2rem;
            color: #718096;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .maintenance-message {
            background: #f7fafc;
            border-left: 4px solid #667eea;
            padding: 1.5rem;
            border-radius: 8px;
            margin: 2rem 0;
            text-align: left;
        }
        
        .maintenance-message h5 {
            color: #2d3748;
            margin-bottom: 1rem;
        }
        
        .maintenance-message p {
            color: #4a5568;
            margin-bottom: 0;
            line-height: 1.6;
        }
        
        .contact-info {
            background: #edf2f7;
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .contact-info h6 {
            color: #2d3748;
            margin-bottom: 1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0.5rem 0;
            color: #4a5568;
        }
        
        .contact-item i {
            width: 20px;
            margin-right: 8px;
            color: #667eea;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            margin-top: 2rem;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .footer-text {
            margin-top: 2rem;
            font-size: 0.9rem;
            color: #a0aec0;
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 2rem 1.5rem;
            }
            
            .maintenance-title {
                font-size: 2rem;
            }
            
            .maintenance-subtitle {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">
            <i class="fas fa-tools"></i>
        </div>
        
        <h1 class="maintenance-title">网站维护中</h1>
        <p class="maintenance-subtitle">
            我们正在对系统进行升级维护，以便为您提供更好的服务体验
        </p>
        
        <div class="maintenance-message">
            <h5><i class="fas fa-info-circle me-2"></i>维护说明</h5>
            <p>
                系统目前正在进行例行维护和功能升级，期间可能无法正常访问。
                我们会尽快完成维护工作，预计很快就能恢复正常服务。
                感谢您的耐心等待！
            </p>
        </div>
        
        <?php if (!empty($settings['contact_qq']) || !empty($settings['contact_wechat']) || !empty($settings['contact_email'])): ?>
        <div class="contact-info">
            <h6><i class="fas fa-headset me-2"></i>联系客服</h6>
            <?php if (!empty($settings['contact_qq'])): ?>
            <div class="contact-item">
                <i class="fab fa-qq"></i>
                <span>QQ：<?php echo htmlspecialchars($settings['contact_qq']); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($settings['contact_wechat'])): ?>
            <div class="contact-item">
                <i class="fab fa-weixin"></i>
                <span>微信：<?php echo htmlspecialchars($settings['contact_wechat']); ?></span>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($settings['contact_email'])): ?>
            <div class="contact-item">
                <i class="fas fa-envelope"></i>
                <span>邮箱：<?php echo htmlspecialchars($settings['contact_email']); ?></span>
            </div>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        
        <a href="javascript:location.reload()" class="back-btn">
            <i class="fas fa-sync-alt me-2"></i>刷新页面
        </a>
        
        <div class="footer-text">
            <?php echo htmlspecialchars($settings['site_name'] ?? '卡密兑换系统'); ?> © <?php echo date('Y'); ?>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
