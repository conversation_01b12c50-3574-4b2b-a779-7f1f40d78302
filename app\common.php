<?php
// 应用公共文件

/**
 * 全局辅助函数
 */

if (!function_exists('site_url')) {
    /**
     * 获取网站URL
     * 自动检测协议、域名和路径，确保在任何安装环境下都能正确工作
     */
    function site_url($path = '')
    {
        // 从数据库获取配置的网站URL
        try {
            $db = \think\facade\Db::connect();
            $siteUrl = $db->table('settings')->where('key', 'site_url')->value('value');

            if (!empty($siteUrl)) {
                $baseUrl = rtrim($siteUrl, '/');
            } else {
                // 自动检测网站URL
                $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

                // 获取脚本路径，去除index.php
                $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
                $basePath = dirname($scriptName);

                // 处理根目录情况
                if ($basePath === '/' || $basePath === '\\') {
                    $basePath = '';
                }

                $baseUrl = $protocol . $host . $basePath;
            }
        } catch (\Exception $e) {
            // 数据库获取失败时使用自动检测
            $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

            // 获取脚本路径，去除index.php
            $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
            $basePath = dirname($scriptName);

            // 处理根目录情况
            if ($basePath === '/' || $basePath === '\\') {
                $basePath = '';
            }

            $baseUrl = $protocol . $host . $basePath;
        }

        // 处理路径参数
        if (!empty($path)) {
            $path = ltrim($path, '/');
            return $baseUrl . '/' . $path;
        }

        return $baseUrl;
    }
}

if (!function_exists('asset_url')) {
    /**
     * 获取静态资源URL
     */
    function asset_url($path = '')
    {
        return site_url($path);
    }
}

if (!function_exists('admin_url')) {
    /**
     * 获取管理后台URL
     */
    function admin_url($path = '')
    {
        $adminPath = 'admin';
        if (!empty($path)) {
            $adminPath .= '/' . ltrim($path, '/');
        }
        return site_url($adminPath);
    }
}
