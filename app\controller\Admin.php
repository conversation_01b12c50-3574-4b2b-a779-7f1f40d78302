<?php

namespace app\controller;

use app\BaseController;
use app\model\AdminUser;
use app\model\LoginAttempt;
use app\model\AccountLockout;
use think\facade\Session;
use think\facade\View;

class Admin extends BaseController
{
    /**
     * 管理员信息
     */
    protected $admin = null;

    /**
     * 初始化
     */
    public function initialize()
    {
        parent::initialize();

        // 自动解锁过期的账户
        AccountLockout::unlockExpiredAccounts();

        // 只有非登录页面才检查登录状态
        $action = $this->request->action();
        if ($action !== 'login') {
            $this->checkLogin();
        }
    }

    /**
     * 检查登录状态
     */
    protected function checkLogin()
    {
        $adminId = Session::get('admin_id');

        if (!$adminId) {
            // 如果是AJAX请求
            if ($this->request->isAjax()) {
                throw new \think\exception\HttpResponseException(json(['code' => 401, 'msg' => '请先登录']));
            }
            // 重定向到登录页
            throw new \think\exception\HttpResponseException(redirect('/admin/login'));
        }

        // 获取管理员信息
        $this->admin = AdminUser::find($adminId);
        if (!$this->admin || $this->admin->status != AdminUser::STATUS_ENABLED) {
            Session::delete('admin_id');
            if ($this->request->isAjax()) {
                throw new \think\exception\HttpResponseException(json(['code' => 401, 'msg' => '账号已被禁用']));
            }
            throw new \think\exception\HttpResponseException(redirect('/admin/login'));
        }

        // 传递管理员信息到视图
        View::assign('admin', $this->admin);
    }

    /**
     * 登录页面
     */
    public function login()
    {
        try {
            // 如果已登录，跳转到控制台
            if (Session::get('admin_id')) {
                if ($this->request->isPost()) {
                    return json(['code' => 1, 'msg' => '已登录', 'url' => '/admin/dashboard']);
                }
                return redirect('/admin/dashboard');
            }

            if ($this->request->isPost()) {
                $username = $this->request->post('username', '');
                $password = $this->request->post('password', '');

                if (empty($username) || empty($password)) {
                    return json(['code' => 0, 'msg' => '用户名和密码不能为空']);
                }

                // 检查账户是否被锁定
                $lockout = AccountLockout::isAccountLocked($username);
                if ($lockout) {
                    $remainingMinutes = $lockout->getRemainingMinutes();
                    return json(['code' => 0, 'msg' => "账户已被锁定，请 {$remainingMinutes} 分钟后再试"]);
                }

                // 查找管理员
                $admin = AdminUser::findByUsername($username);
                if (!$admin) {
                    // 记录失败尝试
                    LoginAttempt::recordAttempt($username, LoginAttempt::STATUS_FAILED, '用户不存在');
                    return json(['code' => 0, 'msg' => '用户名或密码错误']);
                }

                // 验证密码
                if (!$admin->checkPassword($password)) {
                    // 记录失败尝试
                    LoginAttempt::recordAttempt($username, LoginAttempt::STATUS_FAILED, '密码错误');

                    // 检查是否需要锁定账户
                    $this->checkAndLockAccount($username);

                    return json(['code' => 0, 'msg' => '用户名或密码错误']);
                }

                // 登录成功，记录成功尝试
                LoginAttempt::recordAttempt($username, LoginAttempt::STATUS_SUCCESS);

                // 更新登录信息
                $admin->updateLoginInfo();

                // 设置会话
                Session::set('admin_id', $admin->id);
                Session::set('last_activity', time());

                return json(['code' => 1, 'msg' => '登录成功', 'url' => '/admin/dashboard']);
            }

            return View::fetch('admin/simple_login');
        } catch (\Exception $e) {
            if ($this->request->isPost()) {
                return json(['code' => 0, 'msg' => 'Error: ' . $e->getMessage()]);
            }
            return 'Error: ' . $e->getMessage() . '<br>File: ' . $e->getFile() . '<br>Line: ' . $e->getLine();
        }
    }

    /**
     * 退出登录
     */
    public function logout()
    {
        Session::delete('admin_id');
        return redirect('/admin/login');
    }

    /**
     * 检查并锁定账户
     */
    private function checkAndLockAccount($username)
    {
        // 获取系统设置
        $settings = $this->getSystemSettings();
        $failLimit = intval($settings['login_fail_limit'] ?? 5);
        $lockoutDuration = intval($settings['lockout_duration'] ?? 30);

        // 获取失败次数
        $failureCount = LoginAttempt::getFailureCount($username, $lockoutDuration);

        // 如果达到失败限制，锁定账户
        if ($failureCount >= $failLimit) {
            AccountLockout::lockAccount($username, $failureCount, $lockoutDuration);
        }
    }

    /**
     * 获取系统设置
     */
    private function getSystemSettings()
    {
        $db = \think\facade\Db::connect();
        $settings = [];

        $results = $db->table('settings')->select();
        foreach ($results as $setting) {
            $settings[$setting['key']] = $setting['value'];
        }

        return $settings;
    }

    /**
     * 个人资料页面
     */
    public function profile()
    {
        try {
            $adminId = Session::get('admin_id');
            if (!$adminId) {
                return redirect('/admin/login');
            }

            $admin = \app\model\AdminUser::find($adminId);
            if (!$admin) {
                return redirect('/admin/login');
            }

            // 获取系统设置
            $settings = $this->getSystemSettings();

            View::assign('current_page', 'profile');
            View::assign('page_title', '个人资料');
            View::assign('admin', $admin);
            View::assign('settings', $settings);

            return View::fetch('admin/profile');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 更新个人资料
     */
    public function updateProfile()
    {
        try {
            $adminId = Session::get('admin_id');
            if (!$adminId) {
                return json(['code' => 0, 'msg' => '请先登录']);
            }

            $admin = \app\model\AdminUser::find($adminId);
            if (!$admin) {
                return json(['code' => 0, 'msg' => '用户不存在']);
            }

            $data = $this->request->post();

            // 调试信息
            \think\facade\Log::info('Profile update data: ' . json_encode($data));

            // 验证数据
            $validatedData = $this->validateProfileData($data);

            // 调试信息
            \think\facade\Log::info('Validated data: ' . json_encode($validatedData));

            // 处理头像上传
            $avatarFile = $this->request->file('avatar');
            if ($avatarFile && $avatarFile->isValid()) {
                // 使用绝对路径
                $uploadPath = root_path() . 'public/uploads/avatars/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                $fileName = 'avatar_' . $adminId . '_' . date('YmdHis') . '.' . $avatarFile->extension();
                $moveResult = $avatarFile->move($uploadPath, $fileName);

                if ($moveResult) {
                    $validatedData['avatar'] = '/uploads/avatars/' . $fileName;
                    \think\facade\Log::info('Avatar uploaded: ' . $validatedData['avatar']);
                } else {
                    \think\facade\Log::error('Avatar upload failed');
                    return json(['code' => 0, 'msg' => '头像上传失败']);
                }
            }

            // 更新数据
            if (!empty($validatedData)) {
                \think\facade\Log::info('Before update - Admin data: ' . json_encode($admin->toArray()));

                foreach ($validatedData as $key => $value) {
                    $admin->$key = $value;
                }

                $result = $admin->save();

                \think\facade\Log::info('Save result: ' . ($result ? 'success' : 'failed'));
                \think\facade\Log::info('After update - Admin data: ' . json_encode($admin->toArray()));

                if (!$result) {
                    return json(['code' => 0, 'msg' => '数据保存失败']);
                }
            } else {
                \think\facade\Log::info('No data to update');
            }

            return json(['code' => 1, 'msg' => '个人资料更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 修改密码
     */
    public function changePassword()
    {
        try {
            $adminId = Session::get('admin_id');
            if (!$adminId) {
                return json(['code' => 0, 'msg' => '请先登录']);
            }

            $admin = \app\model\AdminUser::find($adminId);
            if (!$admin) {
                return json(['code' => 0, 'msg' => '用户不存在']);
            }

            $oldPassword = $this->request->post('old_password', '');
            $newPassword = $this->request->post('new_password', '');
            $confirmPassword = $this->request->post('confirm_password', '');

            // 验证旧密码
            if (!$admin->checkPassword($oldPassword)) {
                return json(['code' => 0, 'msg' => '原密码错误']);
            }

            // 获取系统设置中的密码最小长度
            $settings = $this->getSystemSettings();
            $minLength = intval($settings['password_min_length'] ?? 6);

            // 验证新密码长度
            if (strlen($newPassword) < $minLength) {
                return json(['code' => 0, 'msg' => "新密码长度不能少于{$minLength}位"]);
            }

            if ($newPassword !== $confirmPassword) {
                return json(['code' => 0, 'msg' => '两次输入的密码不一致']);
            }

            // 更新密码
            $admin->password = password_hash($newPassword, PASSWORD_DEFAULT);
            $admin->save();

            return json(['code' => 1, 'msg' => '密码修改成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '修改失败：' . $e->getMessage()]);
        }
    }

    /**
     * 验证个人资料数据
     */
    private function validateProfileData($data)
    {
        $validated = [];

        // 昵称 - 总是处理，即使为空
        $validated['nickname'] = isset($data['nickname']) ? trim($data['nickname']) : '';

        // 邮箱 - 总是处理，即使为空
        $email = isset($data['email']) ? trim($data['email']) : '';
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('邮箱格式不正确');
        }
        $validated['email'] = $email;

        // 手机号 - 总是处理，即使为空
        $phone = isset($data['phone']) ? trim($data['phone']) : '';
        if (!empty($phone) && !preg_match('/^1[3-9]\d{9}$/', $phone)) {
            throw new \Exception('手机号格式不正确');
        }
        $validated['phone'] = $phone;

        return $validated;
    }

    /**
     * 控制台首页
     */
    public function dashboard()
    {
        try {
            // 获取真实统计数据
            $stats = $this->getDashboardStats();

            // 传递当前页面标识和统计数据
            View::assign('current_page', 'dashboard');
            View::assign('page_title', '控制台');
            View::assign('stats', $stats);

            return View::fetch('admin/dashboard');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取控制台统计数据
     */
    private function getDashboardStats()
    {
        // 卡密统计
        $totalCards = \app\model\Card::count();
        $usedCards = \app\model\Card::where('status', \app\model\Card::STATUS_USED)->count();
        $unusedCards = \app\model\Card::where('status', \app\model\Card::STATUS_UNUSED)->count();
        $disabledCards = \app\model\Card::where('status', \app\model\Card::STATUS_DISABLED)->count();

        // 分类统计
        $totalCategories = \app\model\Category::count();

        // 内容统计
        $totalContents = \app\model\Content::count();

        // 兑换记录统计
        $totalExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)->count();

        // 今日兑换统计
        $todayExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
            ->whereTime('exchange_time', 'today')
            ->count();

        // 本月兑换统计
        $monthExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
            ->whereTime('exchange_time', 'month')
            ->count();

        // 最近活动记录
        $recentActivities = $this->getRecentActivities();

        return [
            'total_cards' => $totalCards,
            'used_cards' => $usedCards,
            'unused_cards' => $unusedCards,
            'disabled_cards' => $disabledCards,
            'total_categories' => $totalCategories,
            'total_contents' => $totalContents,
            'total_exchanges' => $totalExchanges,
            'today_exchanges' => $todayExchanges,
            'month_exchanges' => $monthExchanges,
            'recent_activities' => $recentActivities
        ];
    }

    /**
     * 获取使用趋势数据
     */
    public function getUsageTrend()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $period = $this->request->post('period', 7);
            $period = intval($period);

            // 根据时间段获取数据
            $data = $this->getUsageTrendData($period);

            return json(['code' => 1, 'data' => $data]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取使用趋势数据
     */
    private function getUsageTrendData($period)
    {
        $labels = [];
        $data = [];

        for ($i = $period - 1; $i >= 0; $i--) {
            $date = date('Y-m-d', strtotime("-{$i} days"));
            $labels[] = date('m-d', strtotime($date));

            // 获取当天的兑换次数
            $count = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
                ->whereTime('exchange_time', 'between', [$date . ' 00:00:00', $date . ' 23:59:59'])
                ->count();

            $data[] = $count;
        }

        return [
            'labels' => $labels,
            'data' => $data
        ];
    }

    /**
     * 获取最近活动记录 - 每个卡密只显示一条记录
     */
    private function getRecentActivities()
    {
        $activities = [];
        $cardNumbers = []; // 用于记录已处理的卡密号码

        // 获取最近的卡密记录（按更新时间排序，确保获取最新状态）
        $recentCards = \app\model\Card::with(['category'])
            ->order('update_time', 'desc')
            ->limit(20)
            ->select();

        foreach ($recentCards as $card) {
            // 如果这个卡密号码已经处理过，跳过
            if (in_array($card['card_number'], $cardNumbers)) {
                continue;
            }

            // 检查卡密是否仍然存在（用于检测删除状态）
            $cardExists = \app\model\Card::where('card_number', $card['card_number'])->find();

            // 确定操作类型和时间
            $action = $this->getCardLatestAction($card);
            $actionTime = $this->getCardLatestActionTime($card);

            $activity = [
                'type' => 'card_activity',
                'card_id' => $card['id'],
                'card_number' => $card['card_number'],
                'category_name' => isset($card['category']) ? $card['category']['name'] : '未分类',
                'time' => $actionTime,
                'status' => $card['status'],
                'action' => $action,
                'action_time' => $actionTime,
                'is_deleted' => !$cardExists,
                'use_status' => $this->getUseStatus($card, !$cardExists),
                'enable_status' => $this->getEnableStatus($card['status'], !$cardExists)
            ];
            $activities[] = $activity;
            $cardNumbers[] = $card['card_number']; // 记录已处理的卡密
        }

        // 获取最近的兑换记录，但只处理未在卡密记录中出现的卡密
        $recentExchanges = \app\model\ExchangeRecord::where('status', \app\model\ExchangeRecord::STATUS_SUCCESS)
            ->order('exchange_time', 'desc')
            ->limit(15)
            ->select();

        foreach ($recentExchanges as $exchange) {
            // 如果这个卡密号码已经处理过，跳过
            if (in_array($exchange['card_number'], $cardNumbers)) {
                continue;
            }

            // 检查对应的卡密是否存在
            $card = \app\model\Card::where('card_number', $exchange['card_number'])->find();

            $activity = [
                'type' => 'exchange',
                'card_id' => $card ? $card['id'] : null,
                'card_number' => $exchange['card_number'],
                'category_name' => '兑换记录',
                'time' => $exchange['exchange_time'],
                'status' => 2, // 已使用
                'action' => '兑换成功',
                'action_time' => $exchange['exchange_time'],
                'is_deleted' => !$card,
                'use_status' => $card ? $this->getUseStatus($card, !$card) : '已使用',
                'enable_status' => $card ? $this->getEnableStatus($card['status'], !$card) : '已删除'
            ];
            $activities[] = $activity;
            $cardNumbers[] = $exchange['card_number']; // 记录已处理的卡密
        }

        // 按时间排序并限制数量
        usort($activities, function($a, $b) {
            return strtotime($b['action_time']) - strtotime($a['action_time']);
        });

        return array_slice($activities, 0, 10);
    }

    /**
     * 获取卡密的最新操作类型
     */
    private function getCardLatestAction($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_USED:
                return '卡密使用';
            case \app\model\Card::STATUS_DISABLED:
                return '卡密禁用';
            case \app\model\Card::STATUS_UNUSED:
            default:
                return '新增卡密';
        }
    }

    /**
     * 获取卡密的最新操作时间
     */
    private function getCardLatestActionTime($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_USED:
                return $card['used_time'] ?: $card['update_time'];
            case \app\model\Card::STATUS_DISABLED:
            case \app\model\Card::STATUS_UNUSED:
            default:
                return $card['update_time'] ?: $card['create_time'];
        }
    }

    /**
     * 获取使用状态文本 - 只显示已使用和未使用，基于是否有使用时间判断
     */
    private function getUseStatus($card, $isDeleted = false)
    {
        // 使用状态只看是否有使用时间，不受禁用/启用影响
        if (isset($card['used_time']) && !empty($card['used_time'])) {
            return '已使用';
        } else {
            return '未使用';
        }
    }

    /**
     * 获取启用状态文本 - 显示已禁用、已启用、已删除
     */
    private function getEnableStatus($status, $isDeleted = false)
    {
        // 如果已删除，优先显示已删除状态
        if ($isDeleted) {
            return '已删除';
        }

        switch ($status) {
            case \app\model\Card::STATUS_DISABLED:
                return '已禁用';
            case \app\model\Card::STATUS_UNUSED:
            case \app\model\Card::STATUS_USED:
                return '已启用';
            default:
                return '已启用';
        }
    }

    /**
     * 获取卡密详情
     */
    public function getCardDetail()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $cardId = $this->request->post('card_id');
            if (empty($cardId)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::with(['category'])->find($cardId);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在或已被删除']);
            }

            // 获取关联的内容信息
            $contentIds = json_decode($card['content_ids'], true);
            $contents = [];
            if (!empty($contentIds)) {
                $contents = \app\model\Content::whereIn('id', $contentIds)->select();
            }

            // 获取兑换记录
            $exchangeRecords = \app\model\ExchangeRecord::where('card_number', $card['card_number'])
                ->order('exchange_time', 'desc')
                ->select();

            $cardDetail = [
                'id' => $card['id'],
                'card_number' => $card['card_number'],
                'card_type' => $card['card_type'],
                'batch_id' => $card['batch_id'],
                'category' => $card['category'] ? $card['category']['name'] : '未分类',
                'value' => $card['value'],
                'valid_days' => $card['valid_days'],
                'status' => $card['status'],
                'status_text' => $this->getCardStatusText($card['status']),
                'used_time' => $card['used_time'],
                'used_ip' => $card['used_ip'],
                'expire_time' => $card['expire_time'],
                'create_time' => $card['create_time'],
                'update_time' => $card['update_time'],
                'remark' => $card['remark'],
                'contents' => $contents,
                'exchange_records' => $exchangeRecords
            ];

            return json(['code' => 1, 'data' => $cardDetail]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取详情失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密状态文本
     */
    private function getCardStatusText($status)
    {
        switch ($status) {
            case \app\model\Card::STATUS_UNUSED:
                return '未使用';
            case \app\model\Card::STATUS_USED:
                return '已使用';
            case \app\model\Card::STATUS_DISABLED:
                return '已禁用';
            default:
                return '未知状态';
        }
    }

    /**
     * 获取卡密操作类型
     */
    private function getCardAction($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_UNUSED:
                return '新增卡密';
            case \app\model\Card::STATUS_USED:
                return '卡密使用';
            case \app\model\Card::STATUS_DISABLED:
                return '卡密禁用';
            default:
                return '未知操作';
        }
    }

    /**
     * 获取卡密操作时间
     */
    private function getCardActionTime($card)
    {
        switch ($card['status']) {
            case \app\model\Card::STATUS_USED:
                return $card['used_time'] ?: $card['update_time'];
            default:
                return $card['update_time'];
        }
    }

    /**
     * 获取统计数据
     */
    public function getStats()
    {
        try {
            $db = \think\facade\Db::connect();

            // 获取基本统计
            $totalCards = $db->table('cards')->count();
            $usedCards = $db->table('cards')->where('status', 2)->count();
            $unusedCards = $db->table('cards')->where('status', 1)->count();
            $todayExchange = $db->table('exchange_records')
                               ->whereTime('exchange_time', 'today')
                               ->where('status', 1)
                               ->count();

            // 获取最近兑换记录
            $recentExchanges = $db->table('exchange_records')
                                 ->alias('er')
                                 ->join('cards c', 'er.card_id = c.id', 'left')
                                 ->field('er.card_number, c.card_type, er.exchange_time, er.status')
                                 ->order('er.exchange_time', 'desc')
                                 ->limit(5)
                                 ->select();

            $data = [
                'overview' => [
                    'total_cards' => $totalCards,
                    'used_cards' => $usedCards,
                    'unused_cards' => $unusedCards,
                    'today_exchange' => $todayExchange
                ],
                'recent_exchanges' => $recentExchanges
            ];

            return json(['code' => 1, 'data' => $data]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取统计数据失败：' . $e->getMessage()]);
        }
    }

    /**
     * 卡密管理
     */
    public function cards()
    {
        try {
            // 获取筛选参数
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $page = $this->request->get('page', 1);
            $pageSize = $this->request->get('page_size', 15);
            $limit = in_array($pageSize, [10, 15, 20, 50, 100]) ? $pageSize : 15;

            // 构建查询条件
            $where = [];
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if ($search) {
                $where[] = ['card_number', 'like', '%' . $search . '%'];
            }

            // 获取卡密列表
            $cards = \app\model\Card::with(['category'])
                ->where($where)
                ->order('create_time', 'desc')
                ->paginate([
                    'list_rows' => $limit,
                    'page' => $page,
                ]);

            // 获取关联的内容信息
            foreach ($cards as $card) {
                if ($card->content_ids) {
                    $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);
                    if ($contentIds) {
                        $contents = \app\model\Content::whereIn('id', $contentIds)->column('title');
                        $card->content_titles = $contents;
                    } else {
                        $card->content_titles = [];
                    }
                } else {
                    $card->content_titles = [];
                }
            }

            // 获取卡密概览数据
            $overview = $this->getCardOverview();

            // 自定义分页渲染
            $paginationHtml = $this->renderCustomPaginationForCards($cards);

            View::assign('current_page', 'cards');
            View::assign('page_title', '卡密管理');
            View::assign('cards', $cards);
            View::assign('overview', $overview);
            View::assign('status', $status);
            View::assign('search', $search);
            View::assign('pagination_html', $paginationHtml);
            View::assign('search_params', [
                'status' => $status,
                'search' => $search,
                'page_size' => $limit
            ]);

            return View::fetch('admin/cards');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取卡密概览数据
     */
    private function getCardOverview()
    {
        try {
            // 总卡密数量
            $totalCards = \app\model\Card::count();

            // 未使用卡密数量
            $unusedCards = \app\model\Card::where('status', 1)->count();

            // 已使用卡密数量
            $usedCards = \app\model\Card::where('status', 2)->count();

            // 已禁用卡密数量
            $disabledCards = \app\model\Card::where('status', 0)->count();

            // 今日新增卡密数量
            $todayCards = \app\model\Card::whereTime('create_time', 'today')->count();

            // 本月新增卡密数量
            $monthCards = \app\model\Card::whereTime('create_time', 'month')->count();

            // 卡密总价值（未使用的）
            $totalValue = \app\model\Card::where('status', 1)->sum('value');

            // 已使用卡密价值
            $usedValue = \app\model\Card::where('status', 2)->sum('value');

            return [
                'total_cards' => $totalCards,
                'unused_cards' => $unusedCards,
                'used_cards' => $usedCards,
                'disabled_cards' => $disabledCards,
                'today_cards' => $todayCards,
                'month_cards' => $monthCards,
                'total_value' => $totalValue ?: 0,
                'used_value' => $usedValue ?: 0,
                'unused_rate' => $totalCards > 0 ? round(($unusedCards / $totalCards) * 100, 1) : 0,
                'used_rate' => $totalCards > 0 ? round(($usedCards / $totalCards) * 100, 1) : 0,
            ];
        } catch (\Exception $e) {
            return [
                'total_cards' => 0,
                'unused_cards' => 0,
                'used_cards' => 0,
                'disabled_cards' => 0,
                'today_cards' => 0,
                'month_cards' => 0,
                'total_value' => 0,
                'used_value' => 0,
                'unused_rate' => 0,
                'used_rate' => 0,
            ];
        }
    }

    /**
     * 切换卡密状态
     */
    public function toggleCardStatus()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $status = $this->request->post('status');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::find($id);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            $card->status = intval($status);
            $card->save();

            $statusText = $status == 1 ? '启用' : '禁用';
            return json(['code' => 1, 'msg' => "卡密{$statusText}成功"]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除卡密
     */
    public function deleteCard()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::find($id);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 删除卡密
            $card->delete();

            return json(['code' => 1, 'msg' => '卡密删除成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 批量删除卡密
     */
    public function batchDeleteCards()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $ids = $this->request->post('ids');

            if (empty($ids)) {
                return json(['code' => 0, 'msg' => '请选择要删除的卡密']);
            }

            // 确保ids是数组
            if (!is_array($ids)) {
                $ids = explode(',', $ids);
            }

            // 过滤空值
            $ids = array_filter($ids);

            if (empty($ids)) {
                return json(['code' => 0, 'msg' => '请选择要删除的卡密']);
            }

            $successCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $card = \app\model\Card::find($id);
                    if ($card) {
                        $card->delete();
                        $successCount++;
                    } else {
                        $errors[] = "卡密ID {$id} 不存在";
                    }
                } catch (\Exception $e) {
                    $errors[] = "删除卡密ID {$id} 失败：" . $e->getMessage();
                }
            }

            if ($successCount > 0) {
                $message = "成功删除 {$successCount} 个卡密";
                if (!empty($errors)) {
                    $message .= "，但有 " . count($errors) . " 个失败";
                }
                return json(['code' => 1, 'msg' => $message, 'data' => [
                    'success_count' => $successCount,
                    'errors' => $errors
                ]]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '没有卡密被删除：' . implode('；', $errors)
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密详情
     */
    public function getCardDetails()
    {
        try {
            $id = $this->request->get('id');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '卡密ID不能为空']);
            }

            $card = \app\model\Card::find($id);
            if (!$card) {
                return json(['code' => 0, 'msg' => '卡密不存在']);
            }

            // 获取关联的内容标题
            $contentTitles = [];
            if ($card->content_ids) {
                $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);
                if ($contentIds && is_array($contentIds)) {
                    $contents = \app\model\Content::whereIn('id', $contentIds)->column('title');
                    $contentTitles = array_values($contents);
                }
            }

            // 准备返回数据
            $cardData = $card->toArray();
            $cardData['content_titles'] = $contentTitles;

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $cardData
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取分类下的内容
     */
    public function getCategoryContents()
    {
        try {
            $categoryId = $this->request->get('category_id');

            if (empty($categoryId)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            // 添加调试信息
            \think\facade\Log::info("getCategoryContents: 查询分类ID = " . $categoryId);

            $contents = \app\model\Content::where('category_id', $categoryId)
                ->where('status', 1)
                ->field('id,title,description')
                ->order('sort_order', 'asc')
                ->order('create_time', 'desc')
                ->select();

            // 添加调试信息
            \think\facade\Log::info("getCategoryContents: 查询结果数量 = " . count($contents));
            \think\facade\Log::info("getCategoryContents: 查询结果 = " . json_encode($contents->toArray()));

            return json(['code' => 1, 'data' => $contents, 'debug' => ['category_id' => $categoryId, 'count' => count($contents)]]);

        } catch (\Exception $e) {
            \think\facade\Log::error("getCategoryContents: 异常 = " . $e->getMessage());
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取分类下的内容（备选方法名）
     */
    public function getCategoryContent()
    {
        return $this->getCategoryContents();
    }

    /**
     * 获取指定级别的分类
     */
    public function getCategoriesByLevel()
    {
        try {
            $level = $this->request->get('level');
            $parentId = $this->request->get('parent_id');



            $query = \app\model\Category::where('status', 1);

            if ($parentId !== null && $parentId !== '') {
                // 如果指定了parent_id，获取该父分类下的子分类
                $query->where('parent_id', $parentId);
            } else if ($level == 1 || ($level === null && $parentId === null)) {
                // 获取一级分类（parent_id = 0）
                $query->where('parent_id', 0);
            } else {
                // 其他情况返回空
                return json(['code' => 1, 'data' => []]);
            }

            $categories = $query->field('id,name,parent_id')
                ->order('sort_order', 'asc')
                ->order('id', 'asc')
                ->select();


            return json(['code' => 1, 'data' => $categories]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 检查分类是否有子分类
     */
    public function checkCategoryChildren()
    {
        try {
            $categoryId = $this->request->get('category_id');

            if (empty($categoryId)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $hasChildren = \app\model\Category::where('parent_id', $categoryId)
                ->where('status', 1)
                ->count() > 0;

            return json(['code' => 1, 'data' => ['has_children' => $hasChildren]]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '检查失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取卡密设置
     */
    public function getCardSettings()
    {
        try {
            // 从数据库获取设置
            $settings = $this->getCardSettingsData();
            return json(['code' => 1, 'data' => $settings]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取前端设置
     */
    public function getFrontendSettings()
    {
        try {
            // 从数据库获取前端设置
            $settings = $this->getFrontendSettingsData();
            return json(['code' => 1, 'data' => $settings]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取前端设置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取通知弹窗配置（供前端页面使用）
     */
    public function getNotificationConfig()
    {
        try {
            // 读取前端设置
            $configFile = app()->getRootPath() . 'config/frontend_settings.php';
            $frontendSettings = [];
            if (file_exists($configFile)) {
                $frontendSettings = include $configFile;
            }

            // 提取通知弹窗相关配置
            $notificationConfig = [
                'enabled' => isset($frontendSettings['notification_enabled']) ? (bool)$frontendSettings['notification_enabled'] : false,
                'title' => $frontendSettings['notification_title'] ?? '系统通知',
                'content' => $frontendSettings['notification_content'] ?? '',
                'style' => $frontendSettings['notification_style'] ?? 'info',
                'auto_close' => (int)($frontendSettings['notification_auto_close'] ?? 5),
            ];

            return json(['code' => 1, 'data' => $notificationConfig]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取通知配置失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存系统设置
     */
    public function saveSettings()
    {
        try {
            $data = $this->request->post();

            if (empty($data)) {
                return json(['code' => 0, 'msg' => '没有接收到设置数据']);
            }

            // 处理文件上传
            $uploadedFiles = $this->handleFileUploads();
            if (!empty($uploadedFiles)) {
                $data = array_merge($data, $uploadedFiles);
            }

            // 验证数据
            $validatedData = $this->validateSettingsData($data);

            // 保存到数据库
            $this->saveSettingsToDatabase($validatedData);

            return json(['code' => 1, 'msg' => '设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理文件上传
     */
    private function handleFileUploads()
    {
        $uploadedFiles = [];

        try {
            // 处理logo上传
            $logoFile = $this->request->file('site_logo');
            if ($logoFile && $logoFile->isValid()) {
                // 创建上传目录
                $uploadPath = app()->getRootPath() . 'public/uploads/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // 生成文件名
                $extension = $logoFile->getOriginalExtension();
                $filename = 'logo_' . date('YmdHis') . '.' . $extension;

                // 移动文件
                $logoFile->move($uploadPath, $filename);

                // 返回相对路径
                $uploadedFiles['site_logo'] = '/uploads/' . $filename;
            }

        } catch (\Exception $e) {
            // 文件上传失败时不影响其他设置的保存
        }

        return $uploadedFiles;
    }

    /**
     * 处理前端设置文件上传
     */
    private function handleFrontendFileUploads()
    {
        $uploadedFiles = [];

        try {
            // 处理logo上传
            $logoFile = $this->request->file('site_logo');
            if ($logoFile && $logoFile->isValid()) {
                // 创建上传目录
                $uploadPath = app()->getRootPath() . 'public/uploads/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // 生成文件名
                $extension = $logoFile->getOriginalExtension();
                $filename = 'logo_' . date('YmdHis') . '.' . $extension;

                // 移动文件
                $logoFile->move($uploadPath, $filename);

                // 返回相对路径
                $uploadedFiles['site_logo'] = '/uploads/' . $filename;
            }

        } catch (\Exception $e) {
            // 文件上传失败时不影响其他设置的保存
        }

        return $uploadedFiles;
    }

    /**
     * 验证设置数据
     */
    private function validateSettingsData($data)
    {
        $validated = [];

        // 卡密设置验证
        if (isset($data['card_generate_count'])) {
            $validated['card_generate_count'] = max(1, min(1000, intval($data['card_generate_count'])));
        }
        if (isset($data['card_length'])) {
            $validated['card_length'] = max(4, min(32, intval($data['card_length'])));
        }
        if (isset($data['card_character_type'])) {
            $allowedTypes = ['mixed', 'numbers', 'letters', 'uppercase', 'lowercase', 'alphanumeric'];
            $validated['card_character_type'] = in_array($data['card_character_type'], $allowedTypes) ? $data['card_character_type'] : 'mixed';
        }
        if (isset($data['card_usage_limit'])) {
            $validated['card_usage_limit'] = max(1, min(999, intval($data['card_usage_limit'])));
        }
        if (isset($data['card_prefix'])) {
            $validated['card_prefix'] = substr($data['card_prefix'], 0, 10);
        }
        if (isset($data['card_suffix'])) {
            $validated['card_suffix'] = substr($data['card_suffix'], 0, 10);
        }
        if (isset($data['card_validity_days'])) {
            $validated['card_validity_days'] = max(0, min(3650, intval($data['card_validity_days'])));
        }
        if (isset($data['card_separator'])) {
            $allowedSeparators = ['', '-', '_', '.'];
            $validated['card_separator'] = in_array($data['card_separator'], $allowedSeparators) ? $data['card_separator'] : '';
        }

        if (isset($data['card_success_title'])) {
            $validated['card_success_title'] = $data['card_success_title'];
        }
        if (isset($data['card_success_content'])) {
            $validated['card_success_content'] = $data['card_success_content'];
        }

        // 基本设置验证
        if (isset($data['site_name'])) {
            $validated['site_name'] = $data['site_name'];
        }
        if (isset($data['site_url'])) {
            $validated['site_url'] = $data['site_url'];
        }
        if (isset($data['site_description'])) {
            $validated['site_description'] = $data['site_description'];
        }
        if (isset($data['site_subtitle'])) {
            $validated['site_subtitle'] = $data['site_subtitle'];
        }
        if (isset($data['site_keywords'])) {
            $validated['site_keywords'] = $data['site_keywords'];
        }
        if (isset($data['site_logo'])) {
            $validated['site_logo'] = $data['site_logo'];
        }
        if (isset($data['site_status'])) {
            $validated['site_status'] = $data['site_status'] === '1' ? '1' : '0';
        }

        // 联系方式验证
        if (isset($data['contact_email'])) {
            $validated['contact_email'] = $data['contact_email'];
        }
        if (isset($data['contact_phone'])) {
            $validated['contact_phone'] = $data['contact_phone'];
        }
        if (isset($data['contact_qq'])) {
            $validated['contact_qq'] = $data['contact_qq'];
        }
        if (isset($data['contact_wechat'])) {
            $validated['contact_wechat'] = $data['contact_wechat'];
        }
        if (isset($data['work_time'])) {
            $validated['work_time'] = $data['work_time'];
        }
        if (isset($data['contact_address'])) {
            $validated['contact_address'] = $data['contact_address'];
        }
        if (isset($data['icp_number'])) {
            $validated['icp_number'] = $data['icp_number'];
        }
        if (isset($data['copyright'])) {
            $validated['copyright'] = $data['copyright'];
        }
        if (isset($data['page_footer_notice'])) {
            $validated['page_footer_notice'] = $data['page_footer_notice'];
        }

        // 安全设置验证
        if (isset($data['login_fail_limit'])) {
            $validated['login_fail_limit'] = max(1, min(20, intval($data['login_fail_limit'])));
        }
        if (isset($data['lockout_duration'])) {
            $validated['lockout_duration'] = max(1, min(1440, intval($data['lockout_duration'])));
        }
        if (isset($data['session_timeout'])) {
            $validated['session_timeout'] = max(10, min(1440, intval($data['session_timeout'])));
        }
        if (isset($data['password_min_length'])) {
            $validated['password_min_length'] = max(4, min(20, intval($data['password_min_length'])));
        }


        // 前端设置验证 - 确保所有字段都有值，即使表单中没有提交
        $frontendFields = [
            'site_logo' => '',
            'site_title' => '',
            'promotion_title' => '',
            'promotion_btn1_text' => '',
            'promotion_btn1_url' => '',
            'promotion_btn2_text' => '',
            'promotion_btn2_url' => '',
            'promotion_btn3_text' => '',
            'promotion_btn3_url' => '',
            'promotion_contact_text' => '',
            'promotion_contact_value' => '',
            'exchange_success_message' => '',
            'exchange_error_message' => ''
        ];

        foreach ($frontendFields as $field => $defaultValue) {
            if ($field === 'site_logo') {
                // 文件上传字段特殊处理，如果包含 [object File] 则跳过
                if (isset($data[$field]) && !empty($data[$field]) && strpos($data[$field], '[object File]') === false) {
                    $validated[$field] = $data[$field];
                }
                // 如果是文件对象字符串或空值，则不更新数据库中的值
            } else {
                $validated[$field] = isset($data[$field]) ? $data[$field] : $defaultValue;
            }
        }

        // 复选框字段需要特殊处理，未选中时不会在POST数据中出现
        $validated['promotion_enabled'] = isset($data['promotion_enabled']) && $data['promotion_enabled'] === '1' ? '1' : '0';

        return $validated;
    }

    /**
     * 保存设置到数据库
     */
    private function saveSettingsToDatabase($data)
    {
        $db = \think\facade\Db::connect();

        foreach ($data as $key => $value) {
            // 所有设置都保存到 settings 表
            $tableName = 'settings';

            // 检查设置是否已存在
            $existing = $db->table($tableName)->where('key', $key)->find();

            if ($existing) {
                // 更新现有设置
                $db->table($tableName)->where('key', $key)->update([
                    'value' => $value,
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            } else {
                // 插入新设置
                $db->table($tableName)->insert([
                    'key' => $key,
                    'value' => $value,
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ]);
            }
        }
    }



    /**
     * 分类管理
     */
    public function categories()
    {
        try {
            // 获取所有分类数据（包括禁用的）
            $categories = \app\model\Category::getAllForAdmin();

            // 获取分类统计数据
            $stats = $this->getCategoryStats();

            View::assign('current_page', 'categories');
            View::assign('page_title', '分类管理');
            View::assign('categories', $categories);
            View::assign('stats', $stats);

            return View::fetch('admin/categories');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取分类统计数据
     */
    private function getCategoryStats()
    {
        try {
            // 总分类数
            $totalCategories = \app\model\Category::count();

            // 启用的分类数
            $enabledCategories = \app\model\Category::where('status', 1)->count();

            // 禁用的分类数
            $disabledCategories = \app\model\Category::where('status', 0)->count();

            // 一级分类数
            $level1Categories = \app\model\Category::where('parent_id', 0)->count();

            // 二级分类数
            $level2Categories = \app\model\Category::where('level', 2)->count();

            // 三级分类数
            $level3Categories = \app\model\Category::where('level', 3)->count();

            // 有内容的分类数
            $categoriesWithContent = \app\model\Category::whereExists(function($query) {
                $query->table('contents')->whereRaw('contents.category_id = categories.id');
            })->count();

            return [
                'total' => $totalCategories,
                'enabled' => $enabledCategories,
                'disabled' => $disabledCategories,
                'level1' => $level1Categories,
                'level2' => $level2Categories,
                'level3' => $level3Categories,
                'with_content' => $categoriesWithContent,
                'empty' => $totalCategories - $categoriesWithContent
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'enabled' => 0,
                'disabled' => 0,
                'level1' => 0,
                'level2' => 0,
                'level3' => 0,
                'with_content' => 0,
                'empty' => 0
            ];
        }
    }

    /**
     * 获取分类信息
     */
    public function getCategory()
    {
        try {
            if (!$this->request->isGet()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->get('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'id' => $category->id,
                    'parent_id' => $category->parent_id,
                    'name' => $category->name,
                    'description' => $category->description,
                    'sort_order' => $category->sort_order,
                    'status' => $category->status
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取分类树（用于选择上级分类）
     */
    public function getCategoryTree()
    {
        try {
            $excludeId = $this->request->get('exclude_id', 0); // 排除的分类ID（编辑时排除自己和子级）

            // 获取所有启用的分类
            $categories = \app\model\Category::where('status', 1)
                                           ->order('sort_order', 'asc')
                                           ->order('id', 'asc')
                                           ->select()
                                           ->toArray();

            // 如果有排除ID，需要排除该分类及其所有子级
            if ($excludeId > 0) {
                $categories = $this->filterExcludedCategories($categories, $excludeId);
            }

            // 构建树形结构
            $tree = $this->buildCategoryTree($categories);

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => $tree
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 过滤排除的分类及其子级
     */
    private function filterExcludedCategories($categories, $excludeId)
    {
        $excludeIds = [$excludeId];

        // 递归找出所有子级分类
        $findChildren = function($parentId) use (&$findChildren, $categories, &$excludeIds) {
            foreach ($categories as $category) {
                if ($category['parent_id'] == $parentId) {
                    $excludeIds[] = $category['id'];
                    $findChildren($category['id']);
                }
            }
        };

        $findChildren($excludeId);

        // 过滤掉排除的分类
        return array_filter($categories, function($category) use ($excludeIds) {
            return !in_array($category['id'], $excludeIds);
        });
    }

    /**
     * 构建分类树
     */
    private function buildCategoryTree($categories, $parentId = 0, $level = 0)
    {
        $tree = [];
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['level'] = $level;
                $category['children'] = $this->buildCategoryTree($categories, $category['id'], $level + 1);
                $tree[] = $category;
            }
        }
        return $tree;
    }

    /**
     * 添加/编辑分类
     */
    public function saveCategory()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 验证必填字段
            if (empty($data['name'])) {
                return json(['code' => 0, 'msg' => '分类名称不能为空']);
            }

            // 处理数据
            $categoryData = [
                'name' => trim($data['name']),
                'description' => trim($data['description'] ?? ''),
                'sort_order' => intval($data['sort_order'] ?? 0),
                'status' => intval($data['status'] ?? 1),
                'parent_id' => intval($data['parent_id'] ?? 0),
            ];

            // 计算层级和路径
            if ($categoryData['parent_id'] > 0) {
                $parent = \app\model\Category::find($categoryData['parent_id']);
                if (!$parent) {
                    return json(['code' => 0, 'msg' => '父分类不存在']);
                }
                $categoryData['level'] = $parent->level + 1;
                $categoryData['path'] = $parent->path . ',' . ($data['id'] ?? 'NEW');
            } else {
                $categoryData['level'] = 1;
                $categoryData['path'] = ($data['id'] ?? 'NEW');
            }

            if (!empty($data['id'])) {
                // 编辑分类
                $category = \app\model\Category::find($data['id']);
                if (!$category) {
                    return json(['code' => 0, 'msg' => '分类不存在']);
                }

                // 更新路径
                if ($categoryData['parent_id'] > 0) {
                    $parent = \app\model\Category::find($categoryData['parent_id']);
                    $categoryData['path'] = $parent->path . ',' . $data['id'];
                } else {
                    $categoryData['path'] = $data['id'];
                }

                $category->save($categoryData);
                return json(['code' => 1, 'msg' => '分类更新成功']);
            } else {
                // 添加分类
                $category = \app\model\Category::create($categoryData);

                // 更新路径
                if ($categoryData['parent_id'] > 0) {
                    $parent = \app\model\Category::find($categoryData['parent_id']);
                    $category->path = $parent->path . ',' . $category->id;
                } else {
                    $category->path = $category->id;
                }
                $category->save();

                return json(['code' => 1, 'msg' => '分类添加成功']);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除分类
     */
    public function deleteCategory()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            // 检查是否有子分类
            $children = \app\model\Category::where('parent_id', $id)->select();
            $hasChildren = count($children);

            if ($hasChildren > 0) {
                // 获取子分类名称用于错误提示
                $childNames = [];
                foreach ($children as $child) {
                    $childNames[] = $child->name;
                }
                return json([
                    'code' => 0,
                    'msg' => '该分类下还有 ' . $hasChildren . ' 个子分类，无法删除。子分类：' . implode('、', $childNames)
                ]);
            }

            // 检查是否有关联内容
            $contents = \app\model\Content::where('category_id', $id)->select();
            $hasContents = count($contents);

            if ($hasContents > 0) {
                return json([
                    'code' => 0,
                    'msg' => '该分类下还有 ' . $hasContents . ' 个内容，无法删除'
                ]);
            }

            $category->delete();
            return json(['code' => 1, 'msg' => '分类删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新分类状态
     */
    public function updateCategoryStatus()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $status = $this->request->post('status');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            $newStatus = intval($status);
            $category->status = $newStatus;
            $category->save();

            // 级联操作子分类
            if ($newStatus == 0) {
                $this->disableChildCategories($id);
                $message = '分类及其所有子分类已禁用';
            } else {
                $this->enableChildCategories($id);
                $message = '分类及其所有子分类已启用';
            }

            return json(['code' => 1, 'msg' => $message]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 递归禁用所有子分类
     */
    private function disableChildCategories($parentId)
    {
        // 获取所有子分类
        $children = \app\model\Category::where('parent_id', $parentId)->select();

        foreach ($children as $child) {
            // 禁用子分类
            $child->status = 0;
            $child->save();

            // 递归禁用子分类的子分类
            $this->disableChildCategories($child->id);
        }
    }

    /**
     * 递归启用所有子分类
     */
    private function enableChildCategories($parentId)
    {
        // 获取所有子分类
        $children = \app\model\Category::where('parent_id', $parentId)->select();

        foreach ($children as $child) {
            // 启用子分类
            $child->status = 1;
            $child->save();

            // 递归启用子分类的子分类
            $this->enableChildCategories($child->id);
        }
    }

    /**
     * 更新分类排序
     */
    public function updateCategorySort()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $sortOrder = $this->request->post('sort_order');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            if (!is_numeric($sortOrder)) {
                return json(['code' => 0, 'msg' => '排序值必须是数字']);
            }

            $category = \app\model\Category::find($id);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            $oldSortOrder = $category->sort_order;
            $newSortOrder = intval($sortOrder);

            // 如果排序值没有变化，直接返回成功
            if ($oldSortOrder == $newSortOrder) {
                return json(['code' => 1, 'msg' => '排序值未变化']);
            }

            // 允许相同的排序值，不需要处理冲突
            // 同级分类可以有相同的排序值，系统会按照ID顺序作为次要排序

            $category->sort_order = $newSortOrder;
            $category->save();

            return json([
                'code' => 1,
                'msg' => '排序更新成功',
                'data' => [
                    'id' => $id,
                    'old_sort' => $oldSortOrder,
                    'new_sort' => $newSortOrder,
                    'parent_id' => $category->parent_id
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }



    /**
     * 批量删除分类
     */
    public function batchDeleteCategories()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $input = file_get_contents('php://input');
            $data = json_decode($input, true);

            if (empty($data['ids']) || !is_array($data['ids'])) {
                return json(['code' => 0, 'msg' => '请选择要删除的分类']);
            }

            $ids = $data['ids'];
            $deletedCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $category = \app\model\Category::find($id);
                    if (!$category) {
                        $errors[] = "分类ID {$id} 不存在";
                        continue;
                    }

                    // 检查是否有子分类
                    $hasChildren = \app\model\Category::where('parent_id', $id)->count();
                    if ($hasChildren > 0) {
                        $errors[] = "分类 '{$category->name}' 下还有子分类，无法删除";
                        continue;
                    }

                    // 检查是否有关联内容
                    $hasContents = \app\model\Content::where('category_id', $id)->count();
                    if ($hasContents > 0) {
                        $errors[] = "分类 '{$category->name}' 下还有内容，无法删除";
                        continue;
                    }

                    $category->delete();
                    $deletedCount++;
                } catch (\Exception $e) {
                    $errors[] = "删除分类ID {$id} 失败：" . $e->getMessage();
                }
            }

            if ($deletedCount > 0) {
                $message = "成功删除 {$deletedCount} 个分类";
                if (!empty($errors)) {
                    $message .= "，但有 " . count($errors) . " 个分类删除失败";
                }
                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => [
                        'deleted_count' => $deletedCount,
                        'errors' => $errors
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '没有分类被删除：' . implode('；', $errors)
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '批量删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 内容管理
     */
    public function contents()
    {
        try {
            // 获取筛选参数
            $categoryId = $this->request->get('category_id', '');
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $page = $this->request->get('page', 1);
            $pageSize = $this->request->get('page_size', 10); // 每页显示数量，默认10条

            // 构建查询条件
            $query = \app\model\Content::with(['category']);

            // 分类筛选（包含子分类）
            if (!empty($categoryId)) {
                // 获取该分类及其所有子分类的ID
                $categoryIds = \app\model\Category::getCategoryAndChildrenIds($categoryId);
                if (!empty($categoryIds)) {
                    $query->whereIn('category_id', $categoryIds);
                } else {
                    // 如果没有找到分类ID，则按原来的方式筛选
                    $query->where('category_id', $categoryId);
                }
            }

            // 状态筛选
            if ($status !== '') {
                $query->where('status', intval($status));
            }

            // 搜索筛选
            if (!empty($search)) {
                $query->where('title', 'like', '%' . $search . '%');
            }

            // 分页查询
            $contents = $query->order('sort_order', 'asc')
                             ->order('id', 'desc')
                             ->paginate([
                                 'list_rows' => $pageSize,
                                 'page' => $page,
                                 'query' => $this->request->get(),
                                 'path' => $this->request->baseUrl(),
                                 'fragment' => '',
                                 'var_page' => 'page'
                             ]);

            // 自定义分页渲染
            $paginationHtml = $this->renderCustomPagination($contents);

            // 为每个内容添加完整的分类路径
            foreach ($contents as $content) {
                if ($content['category']) {
                    $content['category_path'] = $this->getCategoryPath($content['category']);
                }
            }

            // 获取内容统计数据
            $stats = $this->getContentStats();

            View::assign('current_page', 'contents');
            View::assign('page_title', '内容管理');
            View::assign('contents', $contents);
            View::assign('stats', $stats);
            View::assign('search_params', [
                'category_id' => $categoryId,
                'status' => $status,
                'search' => $search,
                'page_size' => $pageSize
            ]);
            View::assign('pagination_html', $paginationHtml);

            return View::fetch('admin/contents');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取内容列表（AJAX）
     */
    public function getContentList()
    {
        try {
            // 获取筛选参数
            $categoryId = $this->request->get('category_id', '');
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $page = $this->request->get('page', 1);
            $pageSize = $this->request->get('page_size', 10);
            $limit = in_array($pageSize, [10, 20, 50, 100]) ? $pageSize : 10;

            // 构建查询条件
            $where = [];
            if (!empty($categoryId)) {
                $where[] = ['category_id', '=', $categoryId];
            }
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if (!empty($search)) {
                $where[] = ['title', 'like', '%' . $search . '%'];
            }

            // 获取内容列表
            $contents = \app\model\Content::with(['category'])
                                         ->where($where)
                                         ->order('id', 'desc')
                                         ->paginate([
                                             'list_rows' => $limit,
                                             'page' => $page,
                                             'fragment' => '',
                                             'var_page' => 'page'
                                         ]);

            // 自定义分页渲染
            $paginationHtml = $this->renderCustomPagination($contents);

            // 为每个内容添加完整的分类路径
            foreach ($contents as $content) {
                if ($content['category']) {
                    $content['category_path'] = $this->getCategoryPath($content['category']);
                } else {
                    $content['category_path'] = '未分类';
                }
            }

            // 获取内容统计数据
            $stats = $this->getContentStats();

            return json([
                'code' => 1,
                'msg' => '获取成功',
                'data' => [
                    'contents' => $contents->items(),
                    'pagination_html' => $paginationHtml,
                    'stats' => $stats
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }





    /**
     * 渲染自定义分页
     */
    private function renderCustomPagination($paginator)
    {
        try {
            // 安全获取分页信息
            $currentPage = 1;
            $lastPage = 1;
            $total = 0;

            // 尝试获取分页信息，如果失败则使用默认值
            if (is_object($paginator)) {
                try {
                    if (method_exists($paginator, 'currentPage')) {
                        $currentPage = $paginator->currentPage();
                    }
                    if (method_exists($paginator, 'lastPage')) {
                        $lastPage = $paginator->lastPage();
                    }
                    if (method_exists($paginator, 'total')) {
                        $total = $paginator->total();
                    }
                } catch (\Exception $e) {
                    // 如果获取分页信息失败，使用默认值
                }
            }

            // 如果只有一页或没有数据，显示简单分页
            if ($lastPage <= 1) {
                return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
            }

            // 构建URL
            $baseUrl = $this->request->baseUrl() . '/admin/contents';
            $queryParams = $this->request->get();

            $html = '<nav aria-label="分页导航">';
            $html .= '<ul class="custom-pagination">';

            // 上一页
            if ($currentPage > 1) {
                $queryParams['page'] = $currentPage - 1;
                $previousUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $previousUrl . '">上一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">上一页</span>';
                $html .= '</li>';
            }

            // 计算显示的页码范围
            $start = max(1, $currentPage - 2);
            $end = min($lastPage, $currentPage + 2);

            // 页码
            for ($i = $start; $i <= $end; $i++) {
                if ($i == $currentPage) {
                    $html .= '<li class="custom-page-item active">';
                    $html .= '<span class="custom-page-link active">' . $i . '</span>';
                    $html .= '</li>';
                } else {
                    $queryParams['page'] = $i;
                    $pageUrl = $baseUrl . '?' . http_build_query($queryParams);
                    $html .= '<li class="custom-page-item">';
                    $html .= '<a class="custom-page-link" href="' . $pageUrl . '">' . $i . '</a>';
                    $html .= '</li>';
                }
            }

            // 下一页
            if ($currentPage < $lastPage) {
                $queryParams['page'] = $currentPage + 1;
                $nextUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $nextUrl . '">下一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">下一页</span>';
                $html .= '</li>';
            }

            $html .= '</ul>';
            $html .= '</nav>';

            return $html;

        } catch (\Exception $e) {
            // 如果出现任何错误，返回默认分页
            return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
        }
    }

    /**
     * 渲染卡密页面的自定义分页
     */
    private function renderCustomPaginationForCards($paginator)
    {
        try {
            // 安全获取分页信息
            $currentPage = 1;
            $lastPage = 1;
            $total = 0;

            // 尝试获取分页信息，如果失败则使用默认值
            if (is_object($paginator)) {
                try {
                    if (method_exists($paginator, 'currentPage')) {
                        $currentPage = $paginator->currentPage();
                    }
                    if (method_exists($paginator, 'lastPage')) {
                        $lastPage = $paginator->lastPage();
                    }
                    if (method_exists($paginator, 'total')) {
                        $total = $paginator->total();
                    }
                } catch (\Exception $e) {
                    // 如果获取分页信息失败，使用默认值
                }
            }

            // 如果只有一页或没有数据，显示简单分页
            if ($lastPage <= 1) {
                return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
            }

            // 构建URL
            $baseUrl = $this->request->baseUrl() . '/admin/cards';
            $queryParams = $this->request->get();

            $html = '<nav aria-label="分页导航">';
            $html .= '<ul class="custom-pagination">';

            // 上一页
            if ($currentPage > 1) {
                $queryParams['page'] = $currentPage - 1;
                $previousUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $previousUrl . '">上一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">上一页</span>';
                $html .= '</li>';
            }

            // 计算显示的页码范围
            $start = max(1, $currentPage - 2);
            $end = min($lastPage, $currentPage + 2);

            // 页码
            for ($i = $start; $i <= $end; $i++) {
                if ($i == $currentPage) {
                    $html .= '<li class="custom-page-item active">';
                    $html .= '<span class="custom-page-link active">' . $i . '</span>';
                    $html .= '</li>';
                } else {
                    $queryParams['page'] = $i;
                    $pageUrl = $baseUrl . '?' . http_build_query($queryParams);
                    $html .= '<li class="custom-page-item">';
                    $html .= '<a class="custom-page-link" href="' . $pageUrl . '">' . $i . '</a>';
                    $html .= '</li>';
                }
            }

            // 下一页
            if ($currentPage < $lastPage) {
                $queryParams['page'] = $currentPage + 1;
                $nextUrl = $baseUrl . '?' . http_build_query($queryParams);
                $html .= '<li class="custom-page-item">';
                $html .= '<a class="custom-page-link" href="' . $nextUrl . '">下一页</a>';
                $html .= '</li>';
            } else {
                $html .= '<li class="custom-page-item disabled">';
                $html .= '<span class="custom-page-link disabled">下一页</span>';
                $html .= '</li>';
            }

            $html .= '</ul>';
            $html .= '</nav>';

            return $html;

        } catch (\Exception $e) {
            // 如果出现任何错误，返回默认分页
            return '<nav aria-label="分页导航"><ul class="custom-pagination"><li class="custom-page-item disabled"><span class="custom-page-link disabled">上一页</span></li><li class="custom-page-item active"><span class="custom-page-link active">1</span></li><li class="custom-page-item disabled"><span class="custom-page-link disabled">下一页</span></li></ul></nav>';
        }
    }

    /**
     * 获取分类的完整路径
     */
    private function getCategoryPath($category)
    {
        $path = [];
        $current = $category;

        // 向上追溯到根分类
        while ($current) {
            array_unshift($path, $current['name']);
            if ($current['parent_id'] == 0) {
                break;
            }
            $current = \app\model\Category::find($current['parent_id']);
        }

        return implode(' > ', $path);
    }

    /**
     * 获取分类路径
     */
    public function getCategoryPathApi()
    {
        try {
            $categoryId = $this->request->get('category_id');
            if (empty($categoryId)) {
                return json(['code' => 0, 'msg' => '分类ID不能为空']);
            }

            $category = \app\model\Category::find($categoryId);
            if (!$category) {
                return json(['code' => 0, 'msg' => '分类不存在']);
            }

            $categoryPath = $this->getCategoryPath($category);

            return json([
                'code' => 1,
                'data' => [
                    'category_path' => $categoryPath,
                    'is_leaf' => $category->isLeafNode()
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取单个内容信息
     */
    public function getContent()
    {
        try {
            $id = $this->request->get('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::with(['category'])->find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            // 获取分类路径
            $categoryPath = '';
            if ($content['category']) {
                $categoryPath = $this->getCategoryPath($content['category']);
            }

            return json([
                'code' => 1,
                'data' => [
                    'id' => $content['id'],
                    'title' => $content['title'],
                    'category_id' => $content['category_id'],
                    'content' => $content['content'],
                    'sort_order' => $content['sort_order'],
                    'status' => $content['status'],
                    'category_path' => $categoryPath
                ]
            ]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取失败：' . $e->getMessage()]);
        }
    }

    /**
     * 切换内容状态
     */
    public function toggleContentStatus()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $status = $this->request->post('status');

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            $content->status = intval($status);
            $content->save();

            $statusText = $status == 1 ? '启用' : '禁用';
            return json(['code' => 1, 'msg' => "内容{$statusText}成功"]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '操作失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除内容
     */
    public function deleteContent()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            $content->delete();
            return json(['code' => 1, 'msg' => '内容删除成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '删除失败：' . $e->getMessage()]);
        }
    }



    /**
     * 批量删除内容（包括关联的卡密）
     */
    public function batchDeleteContents()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $ids = $this->request->post('ids', []);

            // 如果ids是JSON字符串，则解码
            if (is_string($ids)) {
                $ids = json_decode($ids, true);
            }

            if (empty($ids) || !is_array($ids)) {
                return json(['code' => 0, 'msg' => '请选择要删除的内容']);
            }

            $deletedContentCount = 0;
            $deletedCardCount = 0;
            $errors = [];

            foreach ($ids as $id) {
                try {
                    $content = \app\model\Content::find($id);
                    if ($content) {
                        // 先删除关联的卡密
                        $relatedCards = \app\model\Card::where('content_ids', 'like', '%"' . $id . '"%')->select();

                        foreach ($relatedCards as $card) {
                            $contentIds = json_decode($card->content_ids, true);
                            if (is_array($contentIds) && in_array($id, $contentIds)) {
                                $card->delete();
                                $deletedCardCount++;
                            }
                        }

                        // 再删除内容
                        $result = $content->delete();
                        if ($result) {
                            $deletedContentCount++;
                        } else {
                            $errors[] = "删除内容《{$content->title}》失败：数据库操作失败";
                        }
                    } else {
                        $errors[] = "内容ID {$id} 不存在";
                    }
                } catch (\Exception $e) {
                    $errors[] = "删除内容ID {$id} 失败：" . $e->getMessage();
                }
            }

            if ($deletedContentCount > 0) {
                $message = "成功删除 {$deletedContentCount} 个内容";
                if ($deletedCardCount > 0) {
                    $message .= "，同时删除了 {$deletedCardCount} 个关联卡密";
                }
                if (!empty($errors)) {
                    $message .= "，但有 " . count($errors) . " 个失败";
                }

                return json([
                    'code' => 1,
                    'msg' => $message,
                    'data' => [
                        'deleted_content_count' => $deletedContentCount,
                        'deleted_card_count' => $deletedCardCount,
                        'errors' => $errors
                    ]
                ]);
            } else {
                return json([
                    'code' => 0,
                    'msg' => '没有内容被删除：' . implode('；', $errors)
                ]);
            }
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '强制删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 保存内容
     */
    public function saveContent()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 验证必填字段
            if (empty($data['title'])) {
                return json(['code' => 0, 'msg' => '内容标题不能为空']);
            }

            if (empty($data['category_id'])) {
                return json(['code' => 0, 'msg' => '请选择分类']);
            }

            if (empty($data['content'])) {
                return json(['code' => 0, 'msg' => '内容详情不能为空']);
            }

            // 验证分类是否为叶子节点
            $category = \app\model\Category::find($data['category_id']);
            if (!$category) {
                return json(['code' => 0, 'msg' => '选择的分类不存在']);
            }

            if (!$category->isLeafNode()) {
                return json(['code' => 0, 'msg' => '只能在最低级分类中添加内容']);
            }

            // 准备保存数据
            $saveData = [
                'title' => $data['title'],
                'category_id' => $data['category_id'],
                'content' => $data['content'],
                'sort_order' => isset($data['sort_order']) ? intval($data['sort_order']) : 0,
                'status' => isset($data['status']) ? 1 : 0,
            ];

            if (isset($data['id']) && !empty($data['id'])) {
                // 编辑内容
                $content = \app\model\Content::find($data['id']);
                if (!$content) {
                    return json(['code' => 0, 'msg' => '内容不存在']);
                }
                $content->save($saveData);
                return json(['code' => 1, 'msg' => '内容更新成功']);
            } else {
                // 新增内容
                \app\model\Content::create($saveData);
                return json(['code' => 1, 'msg' => '内容添加成功']);
            }

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新内容排序
     */
    public function updateContentSort()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $id = $this->request->post('id');
            $sortOrder = $this->request->post('sort_order', 0);

            if (empty($id)) {
                return json(['code' => 0, 'msg' => '内容ID不能为空']);
            }

            $content = \app\model\Content::find($id);
            if (!$content) {
                return json(['code' => 0, 'msg' => '内容不存在']);
            }

            $content->sort_order = intval($sortOrder);
            $content->save();

            return json(['code' => 1, 'msg' => '排序更新成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取内容统计数据
     */
    private function getContentStats()
    {
        try {
            // 总内容数
            $totalContents = \app\model\Content::count();

            // 启用的内容数
            $enabledContents = \app\model\Content::where('status', 1)->count();

            // 禁用的内容数
            $disabledContents = \app\model\Content::where('status', 0)->count();

            // 今日新增内容数
            $todayContents = \app\model\Content::whereTime('create_time', 'today')->count();

            // 各分类的内容数
            $categoryStats = \app\model\Content::with(['category'])
                                              ->where('status', 1)
                                              ->group('category_id')
                                              ->field('category_id, count(*) as content_count')
                                              ->select();

            return [
                'total' => $totalContents,
                'enabled' => $enabledContents,
                'disabled' => $disabledContents,
                'today' => $todayContents,
                'category_stats' => $categoryStats
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'enabled' => 0,
                'disabled' => 0,
                'today' => 0,
                'category_stats' => []
            ];
        }
    }

    /**
     * 系统设置
     */
    public function settings()
    {
        try {
            // 从数据库获取当前设置
            $db = \think\facade\Db::connect();
            $settings = $db->table('settings')->column('value', 'key');

            // 设置默认值
            $defaultSettings = [
                'site_name' => '红嘴鸥教育',
                'site_url' => 'https://kmdh.hzoedu.com',
                'site_description' => '电子资料兑换系统',
                'site_subtitle' => '',
                'site_keywords' => '卡密,兑换,资料,教程',
                'site_status' => '1',
                'site_logo' => '',
                'contact_wechat' => 'hzoedu888',
                'contact_qq' => '',
                'contact_email' => '',
                'contact_phone' => '',
                'work_time' => '周一至周五 9:00-18:00',
                'contact_address' => '',
                'icp_number' => '',
                'copyright' => '© 2024 红嘴鸥教育',
                'page_footer_notice' => '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888',
                'login_fail_limit' => '5',
                'lockout_duration' => '30',
                'session_timeout' => '120',
                'password_min_length' => '6'
            ];

            // 合并默认设置和数据库设置
            $currentSettings = array_merge($defaultSettings, $settings);

            View::assign('current_page', 'settings');
            View::assign('page_title', '系统设置');
            View::assign('settings', $currentSettings);

            return View::fetch('admin/settings');
        } catch (\Exception $e) {
            return 'Error: ' . $e->getMessage();
        }
    }

    /**
     * 获取网站URL
     * 自动检测协议、域名和路径，确保在任何安装环境下都能正确工作
     */
    public static function getSiteUrl()
    {
        // 从数据库获取配置的网站URL
        try {
            $db = \think\facade\Db::connect();
            $siteUrl = $db->table('settings')->where('key', 'site_url')->value('value');

            if (!empty($siteUrl)) {
                return rtrim($siteUrl, '/');
            }
        } catch (\Exception $e) {
            // 数据库获取失败时使用自动检测
        }

        // 自动检测网站URL
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // 获取脚本路径，去除index.php
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($scriptName);

        // 处理根目录情况
        if ($basePath === '/' || $basePath === '\\') {
            $basePath = '';
        }

        return $protocol . $host . $basePath;
    }

    /**
     * 生成绝对URL
     */
    public static function url($path = '')
    {
        $siteUrl = self::getSiteUrl();
        $path = ltrim($path, '/');

        return $siteUrl . ($path ? '/' . $path : '');
    }

    /**
     * 生成卡密
     */
    public function generateCards()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方法错误']);
        }

        try {
            $data = $this->request->post();

            // 新的验证逻辑：只需要分类和内容
            if (empty($data['category_id'])) {
                return json(['code' => 0, 'msg' => '请选择分类']);
            }

            if (empty($data['content_ids'])) {
                return json(['code' => 0, 'msg' => '请选择要关联的内容']);
            }

            // 获取卡密设置
            $settings = $this->getCardSettingsData();
            $quantity = $settings['card_generate_count'];

            // 生成批次ID
            $batchId = 'BATCH' . date('YmdHis');

            // 批量生成卡密
            $db = \think\facade\Db::connect();
            $cards = [];

            for ($i = 0; $i < $quantity; $i++) {
                $cardNumber = $this->generateCardNumberWithSettings($settings);

                // 检查卡密是否已存在
                while ($db->table('cards')->where('card_number', $cardNumber)->find()) {
                    $cardNumber = $this->generateCardNumberWithSettings($settings);
                }

                $expireTime = null;
                if ($settings['card_validity_days'] > 0) {
                    $expireTime = date('Y-m-d H:i:s', time() + $settings['card_validity_days'] * 24 * 3600);
                }

                $cards[] = [
                    'card_number' => $cardNumber,
                    'card_type' => '系统生成',
                    'batch_id' => $batchId,
                    'category_id' => $data['category_id'],
                    'content_ids' => json_encode($data['content_ids']),
                    'value' => 0,
                    'valid_days' => $settings['card_validity_days'],
                    'max_use_count' => $settings['card_usage_limit'],
                    'used_count' => 0,
                    'status' => 1,
                    'expire_time' => $expireTime,
                    'remark' => '根据系统设置自动生成',
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s')
                ];
            }

            // 批量插入
            $db->table('cards')->insertAll($cards);

            // 获取关联的内容标题
            $contentTitles = [];
            if (!empty($data['content_ids'])) {
                $contents = \app\model\Content::whereIn('id', $data['content_ids'])->column('title');
                $contentTitles = array_values($contents);
            }

            // 获取生成的卡密号码
            $cardNumbers = array_column($cards, 'card_number');

            // 获取兑换地址
            $exchangeUrl = $this->getExchangeUrl();

            return json([
                'code' => 1,
                'msg' => "成功生成 {$quantity} 个卡密",
                'data' => [
                    'batch_id' => $batchId,
                    'cards' => $cardNumbers,
                    'content_titles' => $contentTitles,
                    'exchange_url' => $exchangeUrl,
                    'settings' => [
                        'card_success_title' => $settings['card_success_title'],
                        'card_success_content' => $settings['card_success_content']
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '生成卡密失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取兑换地址
     */
    private function getExchangeUrl()
    {
        try {
            $db = \think\facade\Db::connect();
            $siteUrl = $db->table('settings')->where('key', 'site_url')->value('value');

            if (!empty($siteUrl)) {
                return rtrim($siteUrl, '/');
            }
        } catch (\Exception $e) {
            // 数据库获取失败时使用自动检测
        }

        // 自动检测网站URL
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

        // 获取脚本路径，去除admin部分
        $scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
        $basePath = dirname($scriptName);

        // 处理根目录情况
        if ($basePath === '/' || $basePath === '\\') {
            $basePath = '';
        }

        return $protocol . $host . $basePath;
    }

    /**
     * 获取卡密设置数据
     */
    private function getCardSettingsData()
    {
        // 默认设置
        $defaults = [
            'card_generate_count' => 1,
            'card_length' => 8,
            'card_character_type' => 'mixed',
            'card_usage_limit' => 1,
            'card_prefix' => 'HZO-',
            'card_suffix' => '',
            'card_validity_days' => 0,
            'card_separator' => '',

            'card_success_title' => '卡密生成成功',
            'card_success_content' => "内容标题：{CONTENT_TITLES}\n卡密：{CARD_NUMBERS}\n兑换地址：{EXCHANGE_URL}\n\n使用方法：\n1. 复制卡密，点击兑换地址进入\n2. 输入卡密，点击兑换按钮\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）"
        ];

        // 从配置文件获取设置
        $configFile = app()->getRootPath() . 'config/card_settings.php';
        $configSettings = [];
        if (file_exists($configFile)) {
            $configSettings = include $configFile;
        }

        // 合并默认设置和配置文件设置
        return array_merge($defaults, $configSettings);
    }

    /**
     * 获取前端设置数据
     */
    private function getFrontendSettingsData()
    {
        $db = \think\facade\Db::connect();

        // 默认前端设置
        $defaults = [
            'site_logo' => '',
            'site_title' => '红嘴鸥教育',
            'site_description' => '电子资料兑换系统',
            'promotion_enabled' => '1',
            'promotion_title' => '您还可以点击以下按钮获取更多免费资源',
            'promotion_btn1_text' => '电子资料包',
            'promotion_btn1_url' => '#',
            'promotion_btn2_text' => '免费网课',
            'promotion_btn2_url' => '#',
            'promotion_btn3_text' => '官方网站',
            'promotion_btn3_url' => '#',
            'promotion_contact_text' => '唯一售后微信：',
            'promotion_contact_value' => 'hzoedu888',
            'exchange_success_message' => '兑换成功！',
            'exchange_error_message' => '兑换失败，请检查卡密是否正确',
            'page_footer_notice' => '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888',
            // 兑换确认对话框
            'exchange_confirm_title' => '兑换确认',
            'exchange_confirm_content' => '此操作为兑换电子版资料，兑换后不支持任何理由的退换货，点击【确定】按钮，即视为同意！',
            // 验证提示
            'empty_card_message' => '请输入卡密',
            // 兑换错误提示
            'card_not_exist_message' => '卡密不存在或已失效',
            'card_already_used_message' => '您已兑换过该卡密，请使用该卡密点击【兑换记录查询】按钮再次查看！',
            'card_disabled_message' => '卡密已被禁用，请联系微信hzoedu888',
            'card_expired_message' => '卡密已过期',
            // 查询错误提示
            'query_disabled_message' => '卡密已被禁用，暂时无法查看兑换记录，请联系微信hzoedu888',
            'card_not_exchanged_message' => '该卡密尚未兑换，请先兑换后再查看兑换记录',
            'no_exchange_record_message' => '未找到兑换记录',
            // 成功提示
            'query_success_message' => '查询成功',
            // 复制功能提示
            'copy_success_message' => '✅ 链接复制成功！',
            'copy_error_message' => '❌ 复制失败，请手动复制',
            // 网络错误提示
            'network_error_message' => '网络错误，请稍后重试',
        ];

        try {
            // 从 settings 表获取前端设置
            $settings = $db->table('settings')
                          ->whereIn('key', array_keys($defaults))
                          ->column('value', 'key');

            // 合并默认值和数据库值
            return array_merge($defaults, $settings);

        } catch (\Exception $e) {
            return $defaults;
        }
    }

    /**
     * 根据设置生成卡密号码
     */
    private function generateCardNumberWithSettings($settings): string
    {
        $prefix = $settings['card_prefix'] ?? '';
        $suffix = $settings['card_suffix'] ?? '';
        $length = $settings['card_length'] ?? 8;
        $charType = $settings['card_character_type'] ?? 'mixed';
        $separator = $settings['card_separator'] ?? '';

        // 字符集
        $chars = [
            'mixed' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789',
            'numbers' => '0123456789',
            'letters' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'uppercase' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
            'lowercase' => 'abcdefghijklmnopqrstuvwxyz',
            'alphanumeric' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
        ];

        $charSet = $chars[$charType] ?? $chars['mixed'];
        $cardBody = '';

        for ($i = 0; $i < $length; $i++) {
            if ($separator && $i > 0 && $i % 4 === 0) {
                $cardBody .= $separator;
            }
            $cardBody .= $charSet[rand(0, strlen($charSet) - 1)];
        }

        return $prefix . $cardBody . $suffix;
    }

    /**
     * 生成卡密号码（兼容旧方法）
     */
    private function generateCardNumber(): string
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $segments = [];

        for ($i = 0; $i < 4; $i++) {
            $segment = '';
            for ($j = 0; $j < 4; $j++) {
                $segment .= $chars[random_int(0, strlen($chars) - 1)];
            }
            $segments[] = $segment;
        }

        return implode('-', $segments);
    }

    /**
     * 卡密设置
     */
    public function cardSettings()
    {
        try {
            // 读取卡密设置
            $configFile = app()->getRootPath() . 'config/card_settings.php';
            $cardSettings = [];
            if (file_exists($configFile)) {
                $cardSettings = include $configFile;
            }

            // 设置默认值
            $defaultSettings = [
                'card_generate_count' => 1,
                'card_length' => 8,
                'card_character_type' => 'mixed',
                'card_usage_limit' => 1,
                'card_prefix' => 'HZO-',
                'card_suffix' => '',
                'card_validity_days' => 0,
                'card_separator' => '',

                'card_success_title' => '卡密生成成功',
                'card_success_content' => '内容跟标题\n卡密：变量\n兑换地址：https://kmdh.hzoedu.com\n\n使用方法：\n1. 复制卡密，点击兑换地址进入\n2. 输入卡密，点击兑换按钮\n3. 即可获取下载链接\n4. 若需再次查看下载链接，点击【兑换记录查询】\n\n（温馨提示：卡密为一人一码，兑换使用后不支持任何理由退换货）',
            ];

            $cardSettings = array_merge($defaultSettings, $cardSettings);

            View::assign('current_page', 'card-settings');
            View::assign('page_title', '卡密设置');
            View::assign('cardSettings', $cardSettings);
            return View::fetch('admin/card-settings');
        } catch (\Exception $e) {
            return $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 前端设置
     */
    public function frontendSettings()
    {
        try {
            // 读取前端设置
            $configFile = app()->getRootPath() . 'config/frontend_settings.php';
            $frontendSettings = [];
            if (file_exists($configFile)) {
                $frontendSettings = include $configFile;
            }

            // 设置默认值
            $defaultSettings = [
                'site_title' => '',
                'site_description' => '',
                'promotion_enabled' => 0,
                'promotion_title' => '',
                'promotion_btn1_text' => '',
                'promotion_btn1_url' => '',
                'promotion_btn2_text' => '',
                'promotion_btn2_url' => '',
                'promotion_btn3_text' => '',
                'promotion_btn3_url' => '',
                'promotion_contact_text' => '',
                'promotion_contact_value' => '',
                'exchange_success_message' => '',
                'exchange_error_message' => '',
                'page_footer_notice' => '',
                // 兑换确认对话框
                'exchange_confirm_title' => '',
                'exchange_confirm_content' => '',
                // 验证提示
                'empty_card_message' => '',
                // 兑换错误提示
                'card_not_exist_message' => '',
                'card_already_used_message' => '',
                'card_disabled_message' => '',
                'card_expired_message' => '',
                // 查询错误提示
                'query_disabled_message' => '',
                'card_not_exchanged_message' => '',
                'no_exchange_record_message' => '',
                // 成功提示
                'query_success_message' => '',
                // 复制功能提示
                'copy_success_message' => '',
                'copy_error_message' => '',
                // 网络错误提示
                'network_error_message' => '',
                // 通知弹窗设置
                'notification_enabled' => 0,
                'notification_title' => '系统通知',
                'notification_content' => '',
                'notification_style' => 'info',
                'notification_auto_close' => 5,
            ];

            $frontendSettings = array_merge($defaultSettings, $frontendSettings);

            View::assign('current_page', 'frontend-settings');
            View::assign('page_title', '前端设置');
            View::assign('frontendSettings', $frontendSettings);
            return View::fetch('admin/frontend-settings');
        } catch (\Exception $e) {
            return $this->error('页面加载失败：' . $e->getMessage());
        }
    }

    /**
     * 保存卡密设置
     */
    public function saveCardSettings()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 验证必要字段
            $rules = [
                'card_generate_count' => 'require|number|between:1,1000',
                'card_length' => 'require|number|between:4,32',
                'card_character_type' => 'require|in:mixed,numbers,letters,uppercase,lowercase,alphanumeric',
                'card_usage_limit' => 'require|number|between:1,999',
                'card_validity_days' => 'require|number|egt:0',
            ];

            $validate = validate($rules);
            if (!$validate->check($data)) {
                return json(['code' => 0, 'msg' => $validate->getError()]);
            }

            // 这里应该保存到配置文件或数据库
            // 暂时使用文件存储，实际项目中建议使用数据库
            $configFile = app()->getRootPath() . 'config/card_settings.php';
            $configData = [
                'card_generate_count' => (int)$data['card_generate_count'],
                'card_length' => (int)$data['card_length'],
                'card_character_type' => $data['card_character_type'],
                'card_usage_limit' => (int)$data['card_usage_limit'],
                'card_prefix' => $data['card_prefix'] ?? '',
                'card_suffix' => $data['card_suffix'] ?? '',
                'card_validity_days' => (int)$data['card_validity_days'],
                'card_separator' => $data['card_separator'] ?? '',

                'card_success_title' => $data['card_success_title'] ?? '卡密生成成功',
                'card_success_content' => $data['card_success_content'] ?? '',
            ];

            // 使用JSON编码然后转换为PHP数组格式，避免var_export的转义问题
            $configContent = "<?php\nreturn " . $this->arrayToPhpCode($configData) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return json(['code' => 0, 'msg' => '配置文件保存失败']);
            }

            return json(['code' => 1, 'msg' => '卡密设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 将数组转换为PHP代码，正确处理换行符
     */
    private function arrayToPhpCode($array, $indent = 0)
    {
        $indentStr = str_repeat('  ', $indent);
        $result = "array (\n";

        foreach ($array as $key => $value) {
            $result .= $indentStr . "  " . var_export($key, true) . " => ";

            if (is_array($value)) {
                $result .= $this->arrayToPhpCode($value, $indent + 1);
            } else {
                // 对字符串进行特殊处理，保持换行符
                if (is_string($value)) {
                    $result .= "'" . addslashes($value) . "'";
                } else {
                    $result .= var_export($value, true);
                }
            }
            $result .= ",\n";
        }

        $result .= $indentStr . ")";
        return $result;
    }

    /**
     * 保存前端设置
     */
    public function saveFrontendSettings()
    {
        try {
            if (!$this->request->isPost()) {
                return json(['code' => 0, 'msg' => '请求方式错误']);
            }

            $data = $this->request->post();

            // 处理文件上传
            $uploadedFiles = $this->handleFrontendFileUploads();
            if (!empty($uploadedFiles)) {
                $data = array_merge($data, $uploadedFiles);
            }

            // 这里应该保存到配置文件或数据库
            $configFile = app()->getRootPath() . 'config/frontend_settings.php';
            $configData = [
                'site_logo' => $data['site_logo'] ?? '',
                'site_title' => $data['site_title'] ?? '',
                'site_description' => $data['site_description'] ?? '',
                'promotion_enabled' => isset($data['promotion_enabled']) && $data['promotion_enabled'] == '1' ? 1 : 0,
                'promotion_title' => $data['promotion_title'] ?? '',
                'promotion_btn1_text' => $data['promotion_btn1_text'] ?? '',
                'promotion_btn1_url' => $data['promotion_btn1_url'] ?? '',
                'promotion_btn2_text' => $data['promotion_btn2_text'] ?? '',
                'promotion_btn2_url' => $data['promotion_btn2_url'] ?? '',
                'promotion_btn3_text' => $data['promotion_btn3_text'] ?? '',
                'promotion_btn3_url' => $data['promotion_btn3_url'] ?? '',
                'promotion_contact_text' => $data['promotion_contact_text'] ?? '',
                'promotion_contact_value' => $data['promotion_contact_value'] ?? '',
                'exchange_success_message' => $data['exchange_success_message'] ?? '',
                'exchange_error_message' => $data['exchange_error_message'] ?? '',
                'page_footer_notice' => $data['page_footer_notice'] ?? '',
                // 兑换确认对话框
                'exchange_confirm_title' => $data['exchange_confirm_title'] ?? '',
                'exchange_confirm_content' => $data['exchange_confirm_content'] ?? '',
                // 验证提示
                'empty_card_message' => $data['empty_card_message'] ?? '',
                // 兑换错误提示
                'card_not_exist_message' => $data['card_not_exist_message'] ?? '',
                'card_already_used_message' => $data['card_already_used_message'] ?? '',
                'card_disabled_message' => $data['card_disabled_message'] ?? '',
                'card_expired_message' => $data['card_expired_message'] ?? '',
                // 查询错误提示
                'query_disabled_message' => $data['query_disabled_message'] ?? '',
                'card_not_exchanged_message' => $data['card_not_exchanged_message'] ?? '',
                'no_exchange_record_message' => $data['no_exchange_record_message'] ?? '',
                // 成功提示
                'query_success_message' => $data['query_success_message'] ?? '',
                // 复制功能提示
                'copy_success_message' => $data['copy_success_message'] ?? '',
                'copy_error_message' => $data['copy_error_message'] ?? '',
                // 网络错误提示
                'network_error_message' => $data['network_error_message'] ?? '',
                // 通知弹窗设置
                'notification_enabled' => isset($data['notification_enabled']) && $data['notification_enabled'] == '1' ? 1 : 0,
                'notification_title' => $data['notification_title'] ?? '',
                'notification_content' => $data['notification_content'] ?? '',
                'notification_style' => $data['notification_style'] ?? 'info',
                'notification_auto_close' => (int)($data['notification_auto_close'] ?? 5),
            ];

            $configContent = "<?php\nreturn " . var_export($configData, true) . ";\n";

            if (file_put_contents($configFile, $configContent) === false) {
                return json(['code' => 0, 'msg' => '配置文件保存失败']);
            }

            return json(['code' => 1, 'msg' => '前端设置保存成功']);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '保存失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出卡密
     */
    public function exportCards()
    {
        // 检查管理员权限
        if (!Session::has('admin_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }



        try {
            // 获取筛选参数（与cards方法保持一致）
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');

            // 构建查询条件
            $where = [];
            if ($status !== '') {
                $where[] = ['status', '=', $status];
            }
            if ($search) {
                $where[] = ['card_number', 'like', '%' . $search . '%'];
            }

            // 获取所有符合条件的卡密数据
            $cards = \app\model\Card::with(['category'])
                ->where($where)
                ->order('create_time', 'desc')
                ->select();

            if ($cards->isEmpty()) {
                // 创建一个空的Excel文件
                $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
                $sheet = $spreadsheet->getActiveSheet();

                // 设置表头
                $headers = [
                    'A1' => 'ID',
                    'B1' => '卡密号码',
                    'C1' => '卡密类型',
                    'D1' => '批次ID',
                    'E1' => '分类',
                    'F1' => '关联内容',
                    'G1' => '面值',
                    'H1' => '状态',
                    'I1' => '使用次数',
                    'J1' => '最大使用次数',
                    'K1' => '创建时间',
                    'L1' => '使用时间',
                    'M1' => '过期时间',
                    'N1' => '备注'
                ];

                foreach ($headers as $cell => $value) {
                    $sheet->setCellValue($cell, $value);
                }

                // 设置表头样式
                $headerStyle = [
                    'font' => [
                        'bold' => true,
                        'color' => ['rgb' => 'FFFFFF']
                    ],
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => ['rgb' => '4472C4']
                    ],
                    'alignment' => [
                        'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                    ]
                ];
                $sheet->getStyle('A1:N1')->applyFromArray($headerStyle);

                // 添加提示信息
                $sheet->setCellValue('A2', '暂无符合条件的卡密数据');
                $sheet->mergeCells('A2:N2');
                $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

                // 自动调整列宽
                foreach (range('A', 'N') as $column) {
                    $sheet->getColumnDimension($column)->setAutoSize(true);
                }

                // 生成文件名
                $filename = '卡密导出_空数据_' . date('Y-m-d_H-i-s') . '.xlsx';

                // 设置响应头
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                header('Content-Disposition: attachment;filename="' . $filename . '"');
                header('Cache-Control: max-age=0');

                // 输出文件
                $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
                $writer->save('php://output');

                // 清理内存
                $spreadsheet->disconnectWorksheets();
                unset($spreadsheet);

                exit;
            }

            // 使用PhpSpreadsheet创建Excel文件
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头
            $headers = [
                'A1' => 'ID',
                'B1' => '卡密号码',
                'C1' => '卡密类型',
                'D1' => '批次ID',
                'E1' => '分类',
                'F1' => '关联内容',
                'G1' => '面值',
                'H1' => '状态',
                'I1' => '使用次数',
                'J1' => '最大使用次数',
                'K1' => '创建时间',
                'L1' => '使用时间',
                'M1' => '过期时间',
                'N1' => '备注'
            ];

            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
            }

            // 设置表头样式
            $headerStyle = [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                ]
            ];
            $sheet->getStyle('A1:N1')->applyFromArray($headerStyle);

            // 填充数据
            $row = 2;
            foreach ($cards as $card) {
                // 处理内容ID列表 - 参考getCardDetails方法的处理方式
                $contentTitles = [];
                $contentTitlesStr = '';

                if ($card->content_ids) {
                    // 由于Card模型中定义了JSON字段，ThinkPHP会自动处理
                    // 但为了兼容性，我们还是检查一下类型
                    $contentIds = is_array($card->content_ids) ? $card->content_ids : json_decode($card->content_ids, true);

                    if ($contentIds && is_array($contentIds)) {
                        $contents = \app\model\Content::whereIn('id', $contentIds)->column('title');
                        if (!empty($contents)) {
                            $contentTitles = array_values($contents);
                            $contentTitlesStr = implode('、', $contentTitles);
                        }
                    }
                }

                // 如果没有关联内容，显示提示文本
                if (empty($contentTitlesStr)) {
                    $contentTitlesStr = '暂无关联内容';
                }



                // 状态文本
                $statusText = '';
                switch ($card->status) {
                    case \app\model\Card::STATUS_DISABLED:
                        $statusText = '已禁用';
                        break;
                    case \app\model\Card::STATUS_UNUSED:
                        $statusText = '未使用';
                        break;
                    case \app\model\Card::STATUS_USED:
                        $statusText = '已使用';
                        break;
                    default:
                        $statusText = '未知';
                }

                $sheet->setCellValue('A' . $row, $card->id);
                $sheet->setCellValue('B' . $row, $card->card_number);
                $sheet->setCellValue('C' . $row, $card->card_type ?: '通用卡密');
                $sheet->setCellValue('D' . $row, $card->batch_id ?: '');
                $sheet->setCellValue('E' . $row, $card->category ? $card->category->name : '未分类');
                $sheet->setCellValue('F' . $row, $contentTitlesStr);
                $sheet->setCellValue('G' . $row, $card->value);
                $sheet->setCellValue('H' . $row, $statusText);
                $sheet->setCellValue('I' . $row, $card->used_count);
                $sheet->setCellValue('J' . $row, $card->max_use_count);
                $sheet->setCellValue('K' . $row, $card->create_time);
                $sheet->setCellValue('L' . $row, $card->used_time ?: '');
                $sheet->setCellValue('M' . $row, $card->expire_time ?: '');
                $sheet->setCellValue('N' . $row, $card->remark ?: '');

                $row++;
            }

            // 自动调整列宽
            foreach (range('A', 'N') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            // 设置数据区域样式
            $dataRange = 'A2:N' . ($row - 1);
            $dataStyle = [
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                        'color' => ['rgb' => 'CCCCCC']
                    ]
                ],
                'alignment' => [
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                ]
            ];
            $sheet->getStyle($dataRange)->applyFromArray($dataStyle);

            // 生成文件名
            $filename = '卡密导出_' . date('Y-m-d_H-i-s') . '.xlsx';

            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // 输出文件
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save('php://output');

            // 清理内存
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);

            exit;

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导出内容
     */
    public function exportContents()
    {
        // 检查管理员权限
        if (!Session::has('admin_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        try {
            // 获取筛选参数（与contents方法保持一致）
            $categoryId = $this->request->get('category_id', '');
            $status = $this->request->get('status', '');
            $search = $this->request->get('search', '');
            $isTemplate = $this->request->get('template', '0') === '1';

            // 构建查询条件
            $query = \app\model\Content::with(['category']);

            // 分类筛选（包含子分类）
            if (!empty($categoryId)) {
                $categoryIds = \app\model\Category::getCategoryAndChildrenIds($categoryId);
                if (!empty($categoryIds)) {
                    $query->whereIn('category_id', $categoryIds);
                } else {
                    $query->where('category_id', $categoryId);
                }
            }

            // 状态筛选
            if ($status !== '') {
                $query->where('status', intval($status));
            }

            // 搜索筛选
            if (!empty($search)) {
                $query->where('title', 'like', '%' . $search . '%');
            }

            // 如果是模板下载，只获取少量示例数据
            if ($isTemplate) {
                $contents = $query->limit(3)->select();
            } else {
                // 获取所有符合条件的内容数据
                $contents = $query->order('sort_order', 'asc')
                    ->order('create_time', 'desc')
                    ->select();
            }

            // 使用PhpSpreadsheet创建Excel文件
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // 设置表头（只包含实际存在的字段）
            $headers = [
                'A1' => 'ID',
                'B1' => '标题',
                'C1' => '分类',
                'D1' => '分类路径',
                'E1' => '描述',
                'F1' => '详细内容',
                'G1' => '排序权重',
                'H1' => '状态',
                'I1' => '创建时间',
                'J1' => '更新时间'
            ];

            foreach ($headers as $cell => $value) {
                $sheet->setCellValue($cell, $value);
            }

            // 设置表头样式
            $headerStyle = [
                'font' => [
                    'bold' => true,
                    'color' => ['rgb' => 'FFFFFF']
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => '4472C4']
                ],
                'alignment' => [
                    'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
                    'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                ]
            ];
            $sheet->getStyle('A1:J1')->applyFromArray($headerStyle);

            if ($contents->isEmpty() && !$isTemplate) {
                // 添加提示信息
                $sheet->setCellValue('A2', '暂无符合条件的内容数据');
                $sheet->mergeCells('A2:J2');
                $sheet->getStyle('A2')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
            } elseif ($isTemplate && $contents->isEmpty()) {
                // 模板文件添加示例数据
                $sheet->setCellValue('A2', '');
                $sheet->setCellValue('B2', '示例内容标题');
                $sheet->setCellValue('C2', '示例分类');
                $sheet->setCellValue('D2', '一级分类 > 二级分类 > 示例分类');
                $sheet->setCellValue('E2', '这是示例内容的描述');
                $sheet->setCellValue('F2', '这是示例内容的详细内容');
                $sheet->setCellValue('G2', '0');
                $sheet->setCellValue('H2', '启用');
                $sheet->setCellValue('I2', date('Y-m-d H:i:s'));
                $sheet->setCellValue('J2', date('Y-m-d H:i:s'));
            } else {
                // 填充数据
                $row = 2;
                foreach ($contents as $content) {
                    // 获取分类路径
                    $categoryPath = '';
                    if ($content->category) {
                        $categoryPath = $this->getCategoryPath($content->category);
                    }

                    // 状态文本
                    $statusText = $content->status == \app\model\Content::STATUS_ENABLED ? '启用' : '禁用';

                    $sheet->setCellValue('A' . $row, $content->id);
                    $sheet->setCellValue('B' . $row, $content->title);
                    $sheet->setCellValue('C' . $row, $content->category ? $content->category->name : '未分类');
                    $sheet->setCellValue('D' . $row, $categoryPath ?: '未分类');
                    $sheet->setCellValue('E' . $row, $content->description ?: '');
                    $sheet->setCellValue('F' . $row, $content->content ?: '');
                    $sheet->setCellValue('G' . $row, $content->sort_order);
                    $sheet->setCellValue('H' . $row, $statusText);
                    $sheet->setCellValue('I' . $row, $content->create_time);
                    $sheet->setCellValue('J' . $row, $content->update_time);

                    $row++;
                }

                // 设置数据区域样式
                $dataRange = 'A2:J' . ($row - 1);
                $dataStyle = [
                    'borders' => [
                        'allBorders' => [
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                            'color' => ['rgb' => 'CCCCCC']
                        ]
                    ],
                    'alignment' => [
                        'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER
                    ]
                ];
                $sheet->getStyle($dataRange)->applyFromArray($dataStyle);
            }

            // 自动调整列宽
            foreach (range('A', 'J') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }

            // 生成文件名
            if ($isTemplate) {
                $filename = '内容导入模板_' . date('Y-m-d_H-i-s') . '.xlsx';
            } else {
                $filename = '内容导出_' . date('Y-m-d_H-i-s') . '.xlsx';
            }

            // 设置响应头
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // 输出文件
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
            $writer->save('php://output');

            // 清理内存
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);

            exit;

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导出失败：' . $e->getMessage()]);
        }
    }

    /**
     * 导入内容
     */
    public function importContents()
    {
        // 检查管理员权限
        if (!Session::has('admin_id')) {
            return json(['code' => 0, 'msg' => '请先登录']);
        }

        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }

        try {
            // 检查是否有上传文件
            $file = $this->request->file('import_file');
            if (!$file) {
                return json(['code' => 0, 'msg' => '请选择要导入的Excel文件']);
            }

            // 验证文件类型
            $allowedTypes = ['xlsx', 'xls'];
            $fileExtension = strtolower($file->getOriginalExtension());
            if (!in_array($fileExtension, $allowedTypes)) {
                return json(['code' => 0, 'msg' => '只支持Excel文件格式（.xlsx, .xls）']);
            }

            // 验证文件大小（限制10MB）
            if ($file->getSize() > 10 * 1024 * 1024) {
                return json(['code' => 0, 'msg' => '文件大小不能超过10MB']);
            }

            // 保存上传的文件到临时目录
            $tempPath = $file->move(app()->getRuntimePath() . 'temp', uniqid() . '.' . $fileExtension);

            // 读取Excel文件
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader(ucfirst($fileExtension));
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($tempPath);
            $sheet = $spreadsheet->getActiveSheet();
            $data = $sheet->toArray();

            // 删除临时文件
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }

            // 验证数据格式
            if (empty($data) || count($data) < 2) {
                return json(['code' => 0, 'msg' => '文件内容为空或格式不正确']);
            }

            // 验证表头（简化版本）
            $expectedHeaders = ['ID', '标题', '分类', '分类路径', '描述', '详细内容', '排序权重', '状态', '创建时间', '更新时间'];
            $actualHeaders = array_slice($data[0], 0, count($expectedHeaders));

            $headerDiff = array_diff($expectedHeaders, $actualHeaders);
            if (!empty($headerDiff)) {
                return json(['code' => 0, 'msg' => 'Excel文件表头格式不正确，请使用标准导出模板']);
            }

            // 处理数据
            $successCount = 0;
            $errorCount = 0;
            $errors = [];
            $categoryCache = []; // 分类名称到ID的缓存

            // 跳过表头，从第二行开始处理
            for ($i = 1; $i < count($data); $i++) {
                $row = $data[$i];

                // 跳过空行
                if (empty(array_filter($row))) {
                    continue;
                }

                try {
                    // 验证必填字段
                    $title = trim($row[1] ?? '');
                    $categoryName = trim($row[2] ?? '');

                    if (empty($title)) {
                        $errors[] = "第" . ($i + 1) . "行：标题不能为空";
                        $errorCount++;
                        continue;
                    }

                    if (empty($categoryName) || $categoryName === '未分类') {
                        $errors[] = "第" . ($i + 1) . "行：必须指定有效的分类";
                        $errorCount++;
                        continue;
                    }

                    // 查找分类ID
                    $categoryId = null;
                    if (isset($categoryCache[$categoryName])) {
                        $categoryId = $categoryCache[$categoryName];
                    } else {
                        $category = \app\model\Category::where('name', $categoryName)
                            ->where('status', \app\model\Category::STATUS_ENABLED)
                            ->find();
                        if ($category) {
                            // 验证是否为叶子节点
                            if (!$category->isLeafNode()) {
                                $errors[] = "第" . ($i + 1) . "行：分类「{$categoryName}」不是最低级分类，只能在最低级分类中添加内容";
                                $errorCount++;
                                continue;
                            }
                            $categoryId = $category->id;
                            $categoryCache[$categoryName] = $categoryId;
                        } else {
                            $errors[] = "第" . ($i + 1) . "行：分类「{$categoryName}」不存在或已禁用";
                            $errorCount++;
                            continue;
                        }
                    }

                    // 准备保存数据（只包含实际存在的字段）
                    $contentData = [
                        'title' => $title,
                        'category_id' => $categoryId,
                        'description' => trim($row[4] ?? ''),
                        'content' => trim($row[5] ?? ''),
                        'sort_order' => max(0, intval($row[6] ?? 0)),
                        'status' => (trim($row[7] ?? '') === '启用') ? 1 : 0,
                    ];

                    // 检查是否为更新操作（如果有ID且ID存在）
                    $contentId = intval($row[0] ?? 0);
                    if ($contentId > 0) {
                        $existingContent = \app\model\Content::find($contentId);
                        if ($existingContent) {
                            // 更新现有内容
                            $existingContent->save($contentData);
                            $successCount++;
                        } else {
                            // ID不存在，创建新内容
                            \app\model\Content::create($contentData);
                            $successCount++;
                        }
                    } else {
                        // 创建新内容
                        \app\model\Content::create($contentData);
                        $successCount++;
                    }

                } catch (\Exception $e) {
                    $errors[] = "第" . ($i + 1) . "行：" . $e->getMessage();
                    $errorCount++;
                }
            }

            // 清理内存
            $spreadsheet->disconnectWorksheets();
            unset($spreadsheet);

            // 返回结果
            $message = "导入完成！成功：{$successCount}条";
            if ($errorCount > 0) {
                $message .= "，失败：{$errorCount}条";
            }

            return json([
                'code' => 1,
                'msg' => $message,
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'errors' => array_slice($errors, 0, 10) // 只返回前10个错误
                ]
            ]);

        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '导入失败：' . $e->getMessage()]);
        }
    }


}
