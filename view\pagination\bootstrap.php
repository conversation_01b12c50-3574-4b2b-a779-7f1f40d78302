<?php
// +----------------------------------------------------------------------
// | Bootstrap分页模板
// +----------------------------------------------------------------------

if (!$paginator->hasPages()) {
    return '';
}

$previousUrl = $paginator->previousPageUrl();
$nextUrl = $paginator->nextPageUrl();
$currentPage = $paginator->currentPage();
$lastPage = $paginator->lastPage();

// 计算显示的页码范围
$start = max(1, $currentPage - 2);
$end = min($lastPage, $currentPage + 2);

// 如果页数较少，显示所有页码
if ($lastPage <= 7) {
    $start = 1;
    $end = $lastPage;
}

echo '<nav aria-label="分页导航">';
echo '<ul class="custom-pagination">';

// 上一页
if ($previousUrl) {
    echo '<li class="custom-page-item">';
    echo '<a class="custom-page-link" href="' . $previousUrl . '">上一页</a>';
    echo '</li>';
} else {
    echo '<li class="custom-page-item disabled">';
    echo '<span class="custom-page-link disabled">上一页</span>';
    echo '</li>';
}

// 第一页
if ($start > 1) {
    echo '<li class="custom-page-item">';
    echo '<a class="custom-page-link" href="' . $paginator->url(1) . '">1</a>';
    echo '</li>';
    if ($start > 2) {
        echo '<li class="custom-page-item disabled">';
        echo '<span class="custom-page-link disabled">...</span>';
        echo '</li>';
    }
}

// 页码
for ($i = $start; $i <= $end; $i++) {
    if ($i == $currentPage) {
        echo '<li class="custom-page-item active">';
        echo '<span class="custom-page-link active">' . $i . '</span>';
        echo '</li>';
    } else {
        echo '<li class="custom-page-item">';
        echo '<a class="custom-page-link" href="' . $paginator->url($i) . '">' . $i . '</a>';
        echo '</li>';
    }
}

// 最后一页
if ($end < $lastPage) {
    if ($end < $lastPage - 1) {
        echo '<li class="custom-page-item disabled">';
        echo '<span class="custom-page-link disabled">...</span>';
        echo '</li>';
    }
    echo '<li class="custom-page-item">';
    echo '<a class="custom-page-link" href="' . $paginator->url($lastPage) . '">' . $lastPage . '</a>';
    echo '</li>';
}

// 下一页
if ($nextUrl) {
    echo '<li class="custom-page-item">';
    echo '<a class="custom-page-link" href="' . $nextUrl . '">下一页</a>';
    echo '</li>';
} else {
    echo '<li class="custom-page-item disabled">';
    echo '<span class="custom-page-link disabled">下一页</span>';
    echo '</li>';
}

echo '</ul>';
echo '</nav>';
?>
