/* 查询弹窗功能 */

// 获取前端设置的辅助函数
function getFrontendSetting(key, defaultValue = '') {
    return (window.frontendSettings && window.frontendSettings[key]) || defaultValue;
}

// 显示查询弹窗
function showQueryModal() {
    document.getElementById('queryModal').style.display = 'block';
    document.getElementById('queryCardNumber').focus();
}

// 关闭查询弹窗
function closeQueryModal() {
    document.getElementById('queryModal').style.display = 'none';
    document.getElementById('queryCardNumber').value = '';
}

// 查询兑换记录
function queryExchangeRecord() {
    const cardNumber = document.getElementById('queryCardNumber').value.trim();

    if (!cardNumber) {
        AlertModal.warning(getFrontendSetting('empty_card_message', '请输入卡密'));
        return;
    }

    // 保存卡密到全局变量，供后续显示使用
    window.currentQueryCardNumber = cardNumber;

    // 关闭弹窗
    closeQueryModal();
    fetch('/query', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'card_number=' + encodeURIComponent(cardNumber)
    })
    .then(response => {
        return response.json();
    })
    .then(data => {
        if (data.code === 1) {
            // 查询成功，显示简化的查询结果
            let html = '<div class="query-success">';
            html += '<h4>📋 兑换记录查询结果</h4>';

            // 显示简化的卡密信息
            if (data.data && data.data.card_info) {
                const card = data.data.card_info;
                const cardNumber = window.currentQueryCardNumber || '未知';

                html += '<div class="card-info">';
                html += '<p><strong>卡密：</strong>' + cardNumber + '</p>';
                if (card.status) {
                    const statusClass = card.status === '已使用' ? 'status-used' : card.status === '未使用' ? 'status-unused' : 'status-other';
                    html += '<p><strong>状态：</strong><span class="' + statusClass + '">' + card.status + '</span></p>';
                }
                if (card.used_time) {
                    html += '<p><strong>使用时间：</strong>' + card.used_time + '</p>';
                }
                html += '</div>';
            }

            // 显示兑换的内容（与兑换时格式相同）
            if (data.data && data.data.contents && data.data.contents.length > 0) {
                html += '<div style="margin-top: 15px;">';
                html += '<h3 style="text-align: center; color: #155724; font-weight: bold; margin-bottom: 20px;">📚 兑换内容</h3>';

                data.data.contents.forEach(function(content) {
                    // 内容标题
                    html += '<div class="content-item">';
                    html += '<h4>' + content.title + '</h4>';

                    // 显示详细内容 (content字段)
                    if (content.content) {
                        // 将换行符转换为<br>，并保持原有格式
                        let formattedContent = content.content
                            .replace(/\n/g, '<br>')
                            .replace(/\r/g, '');

                        // 使用更精确的方法处理链接
                        // 先标记已处理的链接，避免重复处理
                        let linkCounter = 0;
                        const linkPlaceholders = {};

                        // 处理带协议的链接
                        formattedContent = formattedContent.replace(/(https?:\/\/[^\s<>\[\]]+)/gi, function(match) {
                            const placeholder = '___LINK_PLACEHOLDER_' + (linkCounter++) + '___';
                            linkPlaceholders[placeholder] = '<span class="link-container"><a href="' + match + '" target="_blank" class="content-link">' + match + '</a><button onclick="copyLink(\'' + match + '\')" class="copy-btn" title="复制链接">复制链接</button></span>';
                            return placeholder;
                        });

                        // 处理不带协议的网址
                        formattedContent = formattedContent.replace(/\b((?:www\.)?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(?:\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})*\.[a-zA-Z]{2,}(?:\/[^\s<>\[\]]*)?)\b/gi, function(match) {
                            // 检查是否已经在占位符中
                            if (match.includes('___LINK_PLACEHOLDER_')) return match;

                            const fullUrl = match.startsWith('www.') ? 'https://' + match : 'https://' + match;
                            const placeholder = '___LINK_PLACEHOLDER_' + (linkCounter++) + '___';
                            linkPlaceholders[placeholder] = '<span class="link-container"><a href="' + fullUrl + '" target="_blank" class="content-link">' + match + '</a><button onclick="copyLink(\'' + fullUrl + '\')" class="copy-btn" title="复制链接">复制链接</button></span>';
                            return placeholder;
                        });

                        // 恢复链接占位符
                        for (const placeholder in linkPlaceholders) {
                            formattedContent = formattedContent.replace(placeholder, linkPlaceholders[placeholder]);
                        }

                        // 处理其他高亮内容
                        formattedContent = formattedContent
                            // 识别微信号并高亮显示
                            .replace(/(微信[：:]?\s*[\w\d]+)/gi, '<span class="contact-info">$1</span>')
                            // 识别QQ号并高亮显示
                            .replace(/(QQ[：:]?\s*\d+)/gi, '<span class="contact-info">$1</span>')
                            // 识别其他联系方式
                            .replace(/(客服[：:]?\s*[\w\d]+)/gi, '<span class="contact-info">$1</span>');

                        html += '<div class="content-detail">';
                        html += formattedContent;
                        html += '</div>';
                    }

                    // 显示描述（如果有）
                    if (content.description && content.description !== content.title) {
                        html += '<div class="content-description">';
                        html += content.description;
                        html += '</div>';
                    }

                    html += '</div>';
                });

                html += '</div>';
            }

            html += '</div>';

            // 添加推广模块
            if (data.data.promotion && (data.data.promotion.enabled === '1' || data.data.promotion.enabled === 1)) {
                const promotionHtml = generatePromotionModule(data.data.promotion);
                if (promotionHtml && promotionHtml.trim() !== '') {
                    html += promotionHtml;
                }
            }

            document.getElementById('result').innerHTML = html;
        } else {
            // 查询失败，显示弹窗提示
            AlertModal.error(data.msg);
            // 清空结果区域
            document.getElementById('result').innerHTML = '';
        }
    })
    .catch(error => {
        AlertModal.error(getFrontendSetting('network_error_message', '网络错误，请稍后重试'));
        // 清空结果区域
        document.getElementById('result').innerHTML = '';
    });
}

// 点击弹窗外部关闭弹窗
window.onclick = function(event) {
    const modal = document.getElementById('queryModal');
    if (event.target === modal) {
        closeQueryModal();
    }
}

// 生成推广模块HTML
function generatePromotionModule(promotion) {
    let html = '<div class="promotion-module">';

    // 标题
    if (promotion.title && promotion.title.trim() !== '') {
        html += '<div class="promotion-title">' + promotion.title + '</div>';
    }

    // 按钮组
    let hasButtons = false;
    let buttonsHtml = '<div class="promotion-buttons">';

    if (promotion.buttons && Array.isArray(promotion.buttons)) {
        promotion.buttons.forEach(function(button) {
            if (button.text && button.text.trim() !== '' && button.url && button.url.trim() !== '' && button.url !== '#') {
                buttonsHtml += '<a href="' + button.url + '" target="_blank" class="promotion-btn">' + button.text + '</a>';
                hasButtons = true;
            }
        });
    }
    buttonsHtml += '</div>';

    if (hasButtons) {
        html += buttonsHtml;
    }

    // 联系方式
    if (promotion.contact && promotion.contact.text && promotion.contact.text.trim() !== '' &&
        promotion.contact.value && promotion.contact.value.trim() !== '') {
        html += '<div class="promotion-contact">' + promotion.contact.text + ' <span class="promotion-contact-value">' + promotion.contact.value + '</span></div>';
    }

    html += '</div>';

    // 如果没有任何内容，返回空字符串
    if (!hasButtons && (!promotion.title || promotion.title.trim() === '') &&
        (!promotion.contact || !promotion.contact.text || promotion.contact.text.trim() === '' ||
         !promotion.contact.value || promotion.contact.value.trim() === '')) {
        return '';
    }

    return html;
}

// 弹窗中按回车键查询
document.addEventListener('DOMContentLoaded', function() {
    const queryInput = document.getElementById('queryCardNumber');
    if (queryInput) {
        queryInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                queryExchangeRecord();
            }
        });
    }
});
