<?php

namespace app\model;

use think\Model;

class ExchangeRecord extends Model
{
    protected $table = 'exchange_records';
    
    // 设置字段信息
    protected $schema = [
        'id'            => 'int',
        'card_id'       => 'int',
        'card_number'   => 'string',
        'content_ids'   => 'string',
        'exchange_ip'   => 'string',
        'user_agent'    => 'string',
        'exchange_time' => 'datetime',
        'status'        => 'int',
        'remark'        => 'string',
    ];

    // 自动时间戳
    protected $autoWriteTimestamp = 'exchange_time';
    
    // JSON字段
    protected $json = ['content_ids'];
    
    // 状态常量
    const STATUS_FAILED = 0;   // 失败
    const STATUS_SUCCESS = 1;  // 成功
    
    /**
     * 创建兑换记录
     */
    public static function createRecord($cardId, $cardNumber, $contentIds, $status = self::STATUS_SUCCESS, $remark = null)
    {
        return self::create([
            'card_id' => $cardId,
            'card_number' => $cardNumber,
            'content_ids' => $contentIds,
            'exchange_ip' => request()->ip(),
            'user_agent' => request()->header('user-agent'),
            'status' => $status,
            'remark' => $remark,
        ]);
    }
    
    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $statusMap = [
            self::STATUS_FAILED => '失败',
            self::STATUS_SUCCESS => '成功',
        ];
        
        return $statusMap[$data['status']] ?? '未知';
    }
    
    /**
     * 关联卡密
     */
    public function card()
    {
        return $this->belongsTo(Card::class, 'card_id', 'id');
    }
}
