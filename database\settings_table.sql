-- 系统设置表
CREATE TABLE IF NOT EXISTS `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '设置ID',
  `key` varchar(100) NOT NULL COMMENT '设置键名',
  `value` text COMMENT '设置值',
  `type` varchar(20) DEFAULT 'string' COMMENT '数据类型：string,int,bool,json',
  `group` varchar(50) DEFAULT 'system' COMMENT '配置分组',
  `title` varchar(100) DEFAULT NULL COMMENT '配置标题',
  `description` varchar(500) DEFAULT NULL COMMENT '设置描述',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_key` (`key`),
  KEY `idx_group` (`group`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统设置表';

-- 插入默认设置
INSERT INTO `settings` (`key`, `value`, `description`, `create_time`, `update_time`) VALUES
('card_generate_count', '1', '每次生成卡密数量', NOW(), NOW()),
('card_length', '8', '卡密长度', NOW(), NOW()),
('card_character_type', 'mixed', '字符组合类型', NOW(), NOW()),
('card_usage_limit', '1', '使用次数限制', NOW(), NOW()),
('card_prefix', 'HZO-', '卡密前缀', NOW(), NOW()),
('card_suffix', '', '卡密后缀', NOW(), NOW()),
('card_validity_days', '0', '有效期天数', NOW(), NOW()),
('card_separator', '', '分隔符', NOW(), NOW()),
('card_case_sensitive', '1', '区分大小写', NOW(), NOW()),
('card_auto_delete', '1', '使用后自动删除', NOW(), NOW()),
('card_log_usage', '1', '记录使用日志', NOW(), NOW()),
('card_description', '此卡密仅限本站使用，请妥善保管。如有问题请联系客服。', '卡密说明', NOW(), NOW()),
('site_name', '知识付费系统', '网站名称', NOW(), NOW()),
('site_url', 'http://localhost:8000', '网站地址', NOW(), NOW()),
('site_description', '专业的知识付费平台', '网站描述', NOW(), NOW()),
('site_keywords', '知识付费,在线学习,课程', '网站关键词', NOW(), NOW()),
('contact_email', '<EMAIL>', '联系邮箱', NOW(), NOW()),
('contact_phone', '', '联系电话', NOW(), NOW()),
('contact_qq', '', '联系QQ', NOW(), NOW()),
('contact_wechat', 'hzoedu888', '联系微信', NOW(), NOW()),
-- 前端设置
('site_logo', '', '网站Logo', NOW(), NOW()),
('site_title', '红嘴鸥教育', '网站标题', NOW(), NOW()),
('exchange_success_message', '兑换成功！请查看下方内容', '兑换成功提示', NOW(), NOW()),
('exchange_error_message', '兑换失败，请检查卡密是否正确', '兑换失败提示', NOW(), NOW()),
('page_footer_notice', '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货，点击兑换及代表同意此说明，有问题可以联系客服或微信 hzoedu888', '页面底部说明', NOW(), NOW()),
-- 推广模块设置
('promotion_enabled', '1', '启用推广模块', NOW(), NOW()),
('promotion_title', '您还可以点击以下按钮获取更多免费资源', '推广标题', NOW(), NOW()),
('promotion_btn1_text', '电子资料包', '按钮1文字', NOW(), NOW()),
('promotion_btn1_url', '#', '按钮1链接', NOW(), NOW()),
('promotion_btn2_text', '免费网课', '按钮2文字', NOW(), NOW()),
('promotion_btn2_url', '#', '按钮2链接', NOW(), NOW()),
('promotion_btn3_text', '官方网站', '按钮3文字', NOW(), NOW()),
('promotion_btn3_url', '#', '按钮3链接', NOW(), NOW()),
('promotion_contact_text', '唯一售后微信：', '联系方式文字', NOW(), NOW()),
('promotion_contact_value', 'hzoedu888', '联系方式值', NOW(), NOW()),
-- 其他系统设置
('exchange_notice', '此为电子资料兑换下载系统，兑换后不支持任何理由的退换货', '兑换说明', NOW(), NOW()),
('max_exchange_per_day', '10', '每日最大兑换次数', NOW(), NOW()),
('file_upload_max_size', '10485760', '文件上传大小限制', NOW(), NOW());
