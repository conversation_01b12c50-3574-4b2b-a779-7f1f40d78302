-- 卡密表结构
CREATE TABLE IF NOT EXISTS `cards` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '卡密ID',
  `card_number` varchar(50) NOT NULL COMMENT '卡密号码',
  `card_type` varchar(50) NOT NULL COMMENT '卡密类型',
  `batch_id` varchar(50) DEFAULT NULL COMMENT '批次ID',
  `category_id` int(11) DEFAULT NULL COMMENT '关联分类ID',
  `content_ids` text DEFAULT NULL COMMENT '可兑换内容ID列表（JSON格式）',
  `value` decimal(10,2) DEFAULT 0.00 COMMENT '卡密面值',
  `valid_days` int(11) DEFAULT 0 COMMENT '有效天数，0为永久',
  `max_use_count` int(11) DEFAULT 1 COMMENT '最大使用次数',
  `used_count` int(11) DEFAULT 0 COMMENT '已使用次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1未使用，2已使用，0已禁用',
  `used_time` datetime DEFAULT NULL COMMENT '首次使用时间',
  `used_ip` varchar(45) DEFAULT NULL COMMENT '使用IP',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_card_number` (`card_number`),
  KEY `idx_status` (`status`),
  KEY `idx_batch_id` (`batch_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡密表';
