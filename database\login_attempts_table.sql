-- 登录失败记录表
CREATE TABLE IF NOT EXISTS `login_attempts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `attempt_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '尝试时间',
  `status` tinyint(1) DEFAULT 0 COMMENT '状态：0失败，1成功',
  `failure_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_attempt_time` (`attempt_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录尝试记录表';

-- 账户锁定记录表
CREATE TABLE IF NOT EXISTS `account_lockouts` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '锁定ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `locked_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '锁定时间',
  `unlock_at` datetime NOT NULL COMMENT '解锁时间',
  `failure_count` int(11) DEFAULT 0 COMMENT '失败次数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1锁定中，0已解锁',
  `unlock_reason` varchar(200) DEFAULT NULL COMMENT '解锁原因',
  PRIMARY KEY (`id`),
  KEY `idx_username` (`username`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_locked_at` (`locked_at`),
  KEY `idx_unlock_at` (`unlock_at`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户锁定记录表';
