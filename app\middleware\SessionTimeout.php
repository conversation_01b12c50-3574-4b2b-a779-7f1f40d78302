<?php

namespace app\middleware;

use think\facade\Session;
use think\facade\Db;

class SessionTimeout
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // 只对管理后台进行会话超时检查
        $path = $request->pathinfo();
        if (strpos($path, 'admin') !== 0 || $path === 'admin/login') {
            return $next($request);
        }

        $adminId = Session::get('admin_id');
        if (!$adminId) {
            return $next($request);
        }

        // 获取会话超时设置
        $sessionTimeout = $this->getSessionTimeout();
        
        // 检查最后活动时间
        $lastActivity = Session::get('last_activity');
        $currentTime = time();
        
        if ($lastActivity && ($currentTime - $lastActivity) > ($sessionTimeout * 60)) {
            // 会话超时，清除会话
            Session::delete('admin_id');
            Session::delete('last_activity');
            
            if ($request->isAjax()) {
                return json(['code' => 401, 'msg' => '会话已超时，请重新登录']);
            }
            
            return redirect('/admin/login')->with('timeout_msg', '会话已超时，请重新登录');
        }
        
        // 更新最后活动时间
        Session::set('last_activity', $currentTime);
        
        return $next($request);
    }
    
    /**
     * 获取会话超时设置
     */
    private function getSessionTimeout()
    {
        try {
            $setting = Db::table('settings')->where('key', 'session_timeout')->find();
            return $setting ? intval($setting['value']) : 120; // 默认120分钟
        } catch (\Exception $e) {
            return 120; // 默认120分钟
        }
    }
}
