<?php

namespace app\controller;

use app\BaseController;
use app\model\Content;
use app\model\ExchangeRecord;
use think\facade\Cache;
use think\response\File;

class Download extends BaseController
{
    /**
     * 文件下载
     */
    public function index($id)
    {
        $token = $this->request->get('token', '');
        
        if (empty($token)) {
            return $this->error('下载链接无效');
        }

        // 查找内容
        $content = Content::find($id);
        if (!$content || $content->status != Content::STATUS_ENABLED) {
            return $this->error('文件不存在或已下架');
        }

        // 验证下载权限（这里简化处理，实际应该验证用户是否有权限下载）
        $cacheKey = 'download_token_' . $id . '_' . $token;
        $tokenData = Cache::get($cacheKey);
        
        if (!$tokenData) {
            // 检查是否是有效的兑换记录
            $isValid = $this->validateDownloadPermission($id, $token);
            if (!$isValid) {
                return $this->error('下载权限验证失败');
            }
        }

        // 检查文件是否存在
        $filePath = public_path() . $content->file_path;
        if (!file_exists($filePath)) {
            return $this->error('文件不存在');
        }

        // 更新下载次数
        $content->download_count += 1;
        $content->save();

        // 记录下载日志
        $this->logDownload($content, $this->request->ip());

        // 返回文件下载
        return download($filePath, $content->title . '.' . pathinfo($filePath, PATHINFO_EXTENSION));
    }

    /**
     * 验证下载权限
     */
    private function validateDownloadPermission($contentId, $token)
    {
        // 这里可以实现更复杂的权限验证逻辑
        // 比如检查用户是否已经兑换了包含此内容的卡密
        
        // 简化处理：检查最近的成功兑换记录
        $recentExchange = ExchangeRecord::where('status', ExchangeRecord::STATUS_SUCCESS)
                                       ->where('exchange_time', '>', date('Y-m-d H:i:s', time() - 3600)) // 1小时内
                                       ->where('exchange_ip', $this->request->ip())
                                       ->order('exchange_time', 'desc')
                                       ->find();

        if ($recentExchange) {
            $contentIds = $recentExchange->content_ids ?: [];
            return in_array($contentId, $contentIds);
        }

        return false;
    }

    /**
     * 记录下载日志
     */
    private function logDownload($content, $ip)
    {
        try {
            // 这里可以记录到专门的下载日志表
            trace("文件下载：{$content->title} - IP: {$ip}", 'info');
        } catch (\Exception $e) {
            // 记录失败不影响下载
        }
    }

    /**
     * 错误页面
     */
    private function error($message)
    {
        if ($this->request->isAjax()) {
            return json(['code' => 0, 'msg' => $message]);
        }

        return view('error/download', ['message' => $message]);
    }
}
